package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.constant.XxgkConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.mapper.sys.*;
import com.bjcj.xxgk.model.dto.sys.SysRolePermissionDto;
import com.bjcj.xxgk.model.dto.sys.SysRoleProjectClassifyDto;
import com.bjcj.xxgk.model.pojo.sys.*;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/20 15:08 周二
 */
@Service
public class SysRoleService extends ServiceImpl<SysRoleMapper, SysRole> {

    @Resource
    SysMenuMapper sysMenuMapper;

    @Resource
    SysUserMapper sysUserMapper;

    @Resource
    SysRoleMenuMapper sysRoleMenuMapper;

    @Resource
    SysUserRoleMapper sysUserRoleMapper;

    @Resource
    SysDictMapper sysDictMapper;

    @Resource
    SysRoleProjectClassifyMapper sysRoleProjectClassifyMapper;

    @Transactional(rollbackFor = Exception.class)
    public JsonResult add(SysRole sysRole) {
        if (StrUtil.isNotBlank(sysRole.getId())) {
            return JsonResult.error("新增接口，不允许传id");
        }
        // 查询角色是否已经存在
        SysRole one = getOne(Wrappers.<SysRole>lambdaQuery().eq(SysRole::getCode, sysRole.getCode()));
        if(ObjUtil.isNotNull(one)){
            return JsonResult.error("该角色已存在");
        }
        return save(sysRole) ? JsonResult.success() : JsonResult.error();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult fix(SysRole sysRole) {
        if (StrUtil.isBlank(sysRole.getId())) {
            return JsonResult.error("id是必传的");
        }
        // 修改的角色是管理员的话，直接退出
        if (StrUtil.equals("administrator", sysRole.getCode())) {
            return JsonResult.error("超级管理员不允许修改");
        }
        return updateById(sysRole) ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult roleBelowUser(Page<SysUser> page, String roleId) {
        // 获取字典信息
        String value = sysDictMapper.selectById(XxgkConstant.SHOW_OPERATOR_DICT_ID).getValue();
        return JsonResult.success(sysUserMapper.getUserByRoleId(page, roleId, Boolean.parseBoolean(value)));
    }

    public JsonResult roleBelowMenu(String roleId) {

        return JsonResult.success(
                SysRolePermissionDto.builder()
                        .roleId(roleId)
                        .sysMenuIds(sysMenuMapper.getMenuByRoleId(roleId).stream().map(SysMenu::getId).toList())
                        .build()
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult roleFixMenu(SysRolePermissionDto sysRolePermissionDto) {
        // 删除原有的角色权限
        sysRoleMenuMapper.delete(
                Wrappers.<SysRoleMenu>lambdaQuery()
                        .eq(SysRoleMenu::getRoleId, sysRolePermissionDto.getRoleId())
        );

        // 构建角色权限对象数组
        List<SysRoleMenu> sysRoleMenus = Lists.newArrayList();
        for (String menuId : sysRolePermissionDto.getSysMenuIds()) {
            sysRoleMenus.add(
                    SysRoleMenu.builder().roleId(sysRolePermissionDto.getRoleId()).menuId(menuId).build()
            );
        };
        // 添加新的角色权限-批量插入
        sysRoleMenuMapper.insert(sysRoleMenus);
        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult del(String id) {
        // 删除的角色是管理员的话，直接退出
        if (StrUtil.equals("1828974384542953474", id)) {
            return JsonResult.error("超级管理员不允许删除");
        }
        // 查询角色有没有人员
        boolean exists = sysUserRoleMapper.exists(
                Wrappers.<SysUserRole>lambdaQuery().eq(SysUserRole::getRoleId, id)
        );
        if (exists) {
            return JsonResult.error("该角色下有人员，不允许删除");
        }

        return removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult roleBelowProjectClassify(String roleId) {
        return JsonResult.success(
                SysRoleProjectClassifyDto.builder()
                        .roleId(roleId)
                        .projectClassifyIds(
                                sysDictMapper.getProjectClassifyByRoleId(roleId).stream()
                                        .map(SysDict::getId).toList()
                        )
                        .build()
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult roleFixProjectClassify(SysRoleProjectClassifyDto sysRoleProjectClassifyDto) {
        // 删除原有的角色权限
        sysRoleProjectClassifyMapper.delete(
                Wrappers.<SysRoleProjectClassify>lambdaQuery()
                        .eq(SysRoleProjectClassify::getRoleId, sysRoleProjectClassifyDto.getRoleId())
        );

        // 构建角色权限对象数组
        List<SysRoleProjectClassify> sysRoleProjectClassifies = Lists.newArrayList();
        for (String projectClassifyId : sysRoleProjectClassifyDto.getProjectClassifyIds()) {
            sysRoleProjectClassifies.add(
                    SysRoleProjectClassify.builder()
                            .roleId(sysRoleProjectClassifyDto.getRoleId())
                            .projectClassifyId(projectClassifyId)
                            .build()
            );
        };
        // 添加新的角色权限-批量插入
        sysRoleProjectClassifyMapper.insert(sysRoleProjectClassifies);
        return JsonResult.success();

    }
}
