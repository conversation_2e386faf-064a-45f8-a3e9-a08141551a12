package com.bjcj.xxgk.model.pojo.wflow;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value="file")
public class File {
    /**
    * 主键ID
    */
    @Schema(description="主键ID")
    private String id;

    /**
    * 实例ID
    */
    @Schema(description="实例ID")
    private String instanceId;

    /**
    * 文件id
    */
    @Schema(description="文件id")
    private String fileId;

    /**
    * 文件名称
    */
    @Schema(description="文件名称")
    private String fileName;

    /**
    * 文件类型
    */
    @Schema(description="文件类型")
    private String fileType;

    /**
    * 文件url
    */
    @Schema(description="文件url")
    private String filePath;
}