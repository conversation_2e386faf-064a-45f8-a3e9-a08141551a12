<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysProjectClassifyTemplateMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysProjectClassifyTemplate">
    <!--@mbg.generated-->
    <!--@Table sys_project_classify_template-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="project_classify" jdbcType="VARCHAR" property="projectClassify" />
    <result column="template" jdbcType="VARCHAR" property="template" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_classify, "template", project_name
  </sql>
</mapper>