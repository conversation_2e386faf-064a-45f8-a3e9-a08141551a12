package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:12 周一
 */
@Schema
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_opera_log")
public class SysOperaLog {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private Long id;

    @TableField(value = "\"module\"")
    @Schema(description="功能模块")
    private String module;

    @TableField(value = "\"type\"")
    @Schema(description="操作类型")
    private String type;

    @TableField(value = "\"desc\"")
    @Schema(description="操作描述")
    private String desc;

    @TableField(value = "\"method\"")
    @Schema(description="请求方法")
    private String method;

    @TableField(value = "req_type")
    @Schema(description="请求类型")
    private String reqType;

    @TableField(value = "req_param")
    @Schema(description="请求参数")
    private String reqParam;

    @TableField(value = "res_param")
    @Schema(description="响应参数")
    private String resParam;

    @TableField(value = "user_id")
    @Schema(description="")
    private String userId;

    @TableField(value = "user_name")
    @Schema(description="")
    private String userName;

    @TableField(value = "ip")
    @Schema(description="ip地址")
    private String ip;

    @TableField(value = "uri")
    @Schema(description="uri")
    private String uri;

    @TableField(value = "take_up_time")
    @Schema(description="耗时")
    private Long takeUpTime;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

}