package com.bjcj.xxgk.mapper.message;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.model.dto.message.FileReleasePageRequest;
import com.bjcj.xxgk.model.dto.message.FileReleaseResponse;
import com.bjcj.xxgk.model.pojo.message.FileRelease;
import org.apache.ibatis.annotations.Param;

/**
 * 文件发布Mapper接口
 * <AUTHOR>
 * @date 2024/12/25
 */
public interface FileReleaseMapper extends BaseMapper<FileRelease> {

    /**
     * 分页查询文件发布列表
     * @param page 分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    IPage<FileReleaseResponse> selectFileReleasePage(Page<Object> page, @Param("request") FileReleasePageRequest request);
}
