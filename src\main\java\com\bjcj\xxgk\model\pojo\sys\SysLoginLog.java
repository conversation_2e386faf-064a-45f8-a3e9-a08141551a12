package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-09-30 10:02 周一
 */
@Schema(description = "登录日志表")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_login_log")
public class SysLoginLog {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField(value = "username")
    @Schema(description = "")
    private String username;

    @TableField(value = "user_id")
    @Schema(description = "")
    private String userId;

    @TableField(value = "create_time")
    @Schema(description = "")
    private LocalDateTime createTime;

    /**
     * 登录ip
     */
    @TableField(value = "ip")
    @Schema(description = "登录ip")
    private String ip;

    /**
     * 设备
     */
    @TableField(value = "device")
    @Schema(description = "设备")
    private String device;

    @TableField(value = "login_or_out")
    @Schema(description = "登录或登出")
    private String loginOrOut;

    @TableField(value = "status")
    @Schema(description = "状态")
    private Boolean status;

    @TableField(value = "failed_msg")
    @Schema(description = "失败原因")
    private String failedMsg;
}