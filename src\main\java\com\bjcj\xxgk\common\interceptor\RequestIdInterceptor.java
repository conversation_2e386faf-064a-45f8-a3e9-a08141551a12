package com.bjcj.xxgk.common.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.bjcj.xxgk.model.pojo.sys.SysUser;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 日志拦截器，获取必要的信息
 * <AUTHOR>
 * @date 2025-05-14 09:35 周三
 */
@Slf4j
public class RequestIdInterceptor implements HandlerInterceptor {

//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
//
//        // 生成请求ID并存储到 MDC
//        SysUser sysUser = new SysUser();
//        try {
//            sysUser = (SysUser) StpUtil.getSession().get("sysUser");
//        } catch (Exception e) {
//            log.error("未登录，不做校验");
//        }
//
//        MDC.put("userId", sysUser.getId());
//        MDC.put("username", sysUser.getUsername());
//        return true;
//    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 请求完成后清理 MDC
        MDC.remove("userId");
        MDC.remove("username");
    }


}
