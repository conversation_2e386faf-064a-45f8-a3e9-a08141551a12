<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.wflow.FileMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.wflow.File">
    <!--@mbg.generated-->
    <!--@Table "file"-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="instance_id" jdbcType="VARCHAR" property="instanceId" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, instance_id, file_id, file_name, file_type, file_path
  </sql>

</mapper>