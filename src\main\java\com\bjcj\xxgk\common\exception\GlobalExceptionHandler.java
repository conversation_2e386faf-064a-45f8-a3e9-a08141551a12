package com.bjcj.xxgk.common.exception;

import cn.dev33.satoken.exception.SaTokenException;
import com.bjcj.xxgk.common.domain.Code;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.domain.Message;
import com.bjcj.xxgk.mapper.sys.ConstraintMapper;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintViolation;
import lombok.extern.slf4j.Slf4j;
import org.postgresql.util.PSQLException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-08-19 14:54
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Resource
    ConstraintMapper constraintMapper;

    /**
     * 自定义异常
     */
    @ExceptionHandler(BusinessException.class)
    public JsonResult businessException(BusinessException e) {
        log.error("错误信息:{}", e.getLocalizedMessage());
        return JsonResult.build(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(CaptchaException.class)
    public JsonResult captchaException(CaptchaException e) {
        log.error("错误信息:{}", e.getLocalizedMessage());
        return JsonResult.error(e.getMessage());
    }

    /**
     * @validated 参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public JsonResult parameterExceptionHandler(MethodArgumentNotValidException e) {
        // 获取异常信息
        BindingResult bindingResult = e.getBindingResult();
        StringBuilder sb = new StringBuilder();
        for (FieldError error : bindingResult.getFieldErrors()) {
            String field = error.getField();
            Object value = error.getRejectedValue();
            String msg = error.getDefaultMessage();
            String message = String.format("错误字段：%s，错误值：%s，原因：%s；", field, value, msg);
            sb.append(message).append("\r\n");
        }
        return JsonResult.build(Code.BAD_REQUEST.value(), sb.toString());
    }

    /**
     * 处理方法的实体参数异常
     */
    @ExceptionHandler(BindException.class)
    public JsonResult handleValidException(BindException e) {
        Map<String, String> errorMap = new HashMap<>();
        e.getBindingResult().getFieldErrors().forEach(r -> errorMap.put(r.getField(), r.getDefaultMessage()));
        log.error("错误信息: {}", errorMap);
        return JsonResult.build(Code.BAD_REQUEST.value(), errorMap.toString());
    }

    /**
     * @validated 校验异常
     */
    @ExceptionHandler(jakarta.validation.ConstraintViolationException.class)
    public JsonResult constraintViolationException(jakarta.validation.ConstraintViolationException e) {
        StringBuilder sb = new StringBuilder();
        for (ConstraintViolation<?> violation : e.getConstraintViolations()) {
            sb.append(violation.getPropertyPath()).append(": ").append(violation.getMessage());
        }
        return JsonResult.build(Code.BAD_REQUEST.value(), sb.toString());
    }

    /**
     * 参数缺失异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public JsonResult parameterMissingExceptionHandler(MissingServletRequestParameterException e) {
        log.error("", e);
        return JsonResult.build(Code.BAD_REQUEST.value(),
                "请求参数 " + e.getParameterName() + " 是必传的哦 ٩(๑>◡<๑)۶ ");
    }

    /**
     * 参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public JsonResult methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        log.error("错误信息{}", e.getLocalizedMessage());
        return JsonResult.build(HttpStatus.BAD_REQUEST.value(), Message.ARGUMENT_TYPE_MISMATCH.message());
    }

    /**
     * 请求方式不支持
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public JsonResult httpReqMethodNotSupported(HttpRequestMethodNotSupportedException e) {
        log.error("错误信息:", e);
        return JsonResult.build(HttpStatus.BAD_REQUEST.value(), Message.REQ_METHOD_NOT_SUPPORT.message());
    }

    @ExceptionHandler(SaTokenException.class)
    public JsonResult handlerSaTokenException(SaTokenException e) {
        log.error("错误信息::", e);
        return switch (e.getCode()) {
            case 11011, 11012, 11013, 11014, 11015, 11016 -> JsonResult.build(11011, e.getMessage());
            case 11041, 11051 -> JsonResult.build(500, e.getMessage());
            default -> JsonResult.error(e.getMessage());
        };
    }

    // 数据库相关异常
    @ExceptionHandler(DataIntegrityViolationException.class)
    public JsonResult handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        Throwable rootCause = ex.getRootCause();

        if (rootCause != null) {
            // 检查底层原因的类型
            if (rootCause instanceof PSQLException) {
                PSQLException psqlException = (PSQLException) rootCause;

                // 根据 PostgreSQL 的错误代码进一步处理
                // 23505 表示唯一约束违反
                if ("23505".equals(psqlException.getSQLState())) {
                    return JsonResult.error(
                            constraintMapper.getConstraintComment(psqlException.getServerErrorMessage().getConstraint())
                    );
                }
            }
        }

        return JsonResult.error("Data integrity violation: " + ex.getMessage());
    }


    /**
     * 全局异常
     */
    @ExceptionHandler(Exception.class)
    public JsonResult handlerCommerceException(Exception ex) {
        log.error("错误信息::", ex);
        return JsonResult.build(Code.ERROR.value(), ex.getLocalizedMessage());
    }
}
