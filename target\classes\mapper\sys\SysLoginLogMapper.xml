<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysLoginLogMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysLoginLog">
        <!--@mbg.generated-->
        <!--@Table sys_login_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="ip" jdbcType="VARCHAR" property="ip"/>
        <result column="device" jdbcType="VARCHAR" property="device"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, username, user_id, create_time, ip, device
    </sql>
</mapper>