package com.bjcj.xxgk.model.dto.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/4 15:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SysRolePermissionDto {

    @Schema(description = "角色id")
    private String roleId;

    @Schema(description = "菜单id集合")
    private List<String> sysMenuIds;

}
