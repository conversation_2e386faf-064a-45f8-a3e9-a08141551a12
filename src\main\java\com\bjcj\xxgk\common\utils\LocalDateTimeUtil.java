package com.bjcj.xxgk.common.utils;

import cn.hutool.core.util.StrUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-29 09:12
 */
public class LocalDateTimeUtil {
    
    /**
     * @desc 当前时间
     
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-09-29 09:13
     */
    public static String now() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * <h2>当前时间 斜杠 格式 </h2>
     * eg: 2024/12/12
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/8/16 15:04
     */
    public static String slashNowDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
    }

    /**
     * <h2>带横杠的年月日</h2>
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/10/23 14:55
     */
    public static String barreNowDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * <h2>不带横杠的年月日</h2>
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/10/23 14:55
     */
    public static String nowDate() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    public static String timestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    /**
     * <h2>获取前几个月的月份列表</h2>
     * @param localDate: 起始月份，可传null
     * @param span: 跨度，前几个月，包含
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/11/23 10:11
     */
    public static List<String> getBeforeMonth(LocalDate localDate, int span) {
        List<String> result = new ArrayList<>();
        if (null == localDate) {
            localDate = LocalDate.now();
        }
        for (int i = 0; i <= span; i++) {
            LocalDate lastMonth = localDate.minusMonths(i);
            result.add(lastMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }
        return result;
    }

    /**
     * <h2>日期后面补零 </h2>
     * eg: 2024-12-12 -> 2024-12-12 00:00:00
     * @param date: 需要补零的日期
     * @return java.time.LocalDateTime
     * <AUTHOR>
     * @date 2024/8/16 15:25
     */
    public static LocalDateTime zeroFill(String date) {
        if (StrUtil.isNotBlank(date)) {
            LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return localDate.atStartOfDay();
        }
        return LocalDateTime.now();
    }

    public static LocalDateTime zeroFillPlusDay(String date, Long day) {
        return zeroFill(date).plusDays(day);
    }

    // public static String distance(LocalDateTime time1, LocalDateTime time2) {
        // if (time1.isAfter(time2)) {
        //     LocalDateTime time3 = time1;
        //     time1 = time2;
        //     time2 = time3;
        // }
    //     Duration duration = Duration.between(time1, time2);
    //     return null;
    // }
    
    public static void main(String[] args) {
//        LocalDateTime time1 = LocalDateTime.now();
//        LocalDateTime time2 = LocalDateTime.parse("2021-12-16T14:00:00");
//        System.out.println(LocalTime.ofSecondOfDay(Duration.between(time1, time2).getSeconds()));

//        LocalDate localDate = LocalDate.now();
//        System.out.println(localDate.minusMonths(1));
//         System.out.println(getBeforeMonth(null, 2));
        System.out.println(zeroFill("2024-08-15"));
    }
    
}
