package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.common.constant.OperaLogConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.dto.sys.JobMoveDto;
import com.bjcj.xxgk.model.dto.sys.PasswordDto;
import com.bjcj.xxgk.model.dto.sys.SysUserDto;
import com.bjcj.xxgk.serviceImpl.sys.SysUserService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/8/20 18:36
 */
@SaCheckLogin
@RestController
@RequestMapping("/user")
@Tag(name = "02用户")
@Validated
public class UserController {
    @Resource
    private SysUserService sysUserService;

    @Operation(summary = "用户分页列表", description = "用户分页列表")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult userPage(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize,
                               String departmentId, String jobId, Short status, String username) {
        return sysUserService.userPage(
                new Page<>(pageNum, pageSize), departmentId, jobId, status, username
        );
    }

    @Operation(summary = "用户回显", description = "用户回显")
    @ApiOperationSupport(order = 2)
    @GetMapping("/{id}")
    public JsonResult user(@PathVariable("id") String id) {
        return sysUserService.user(id);
    }

    @SaCheckPermission("system:user:add")
    @PostMapping
    @Operation(summary = "新增用户", description = "新增用户")
    @ApiOperationSupport(order = 3)
    @OperaLog(operaModule = "新增用户",operaType = OperaLogConstant.CREATE)
    public JsonResult add(@Validated @RequestBody SysUserDto sysUser) {
        return sysUserService.add(sysUser);
    }

    @SaCheckPermission("system:user:edit")
    @PutMapping
    @Operation(summary = "编辑用户", description = "编辑用户")
    @ApiOperationSupport(order = 4)
    @OperaLog(operaModule = "编辑用户",operaType = OperaLogConstant.UPDATE)
    public JsonResult fix(@Validated @RequestBody SysUserDto sysUser) {
        return sysUserService.fix(sysUser);
    }

    @SaCheckPermission("system:user:delete")
    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "删除用户")
    @ApiOperationSupport(order = 5)
    @OperaLog(operaModule = "删除用户",operaType = OperaLogConstant.DELETE)
    public JsonResult delete(@PathVariable("id") String id) {
        return sysUserService.delete(id);
    }

    @Operation(summary = "用户登录后的全部信息", description = "用户登录后的全部信息")
    @ApiOperationSupport(order = 6)
    @GetMapping("/info")
    public JsonResult userInfo() {
        return sysUserService.userInfo();
    }

    @SaCheckPermission("system:user:status")
    @PutMapping("/{enable}/{id}")
    @Operation(summary = "用户启禁用", description = "用户启禁用")
    @ApiOperationSupport(order = 7)
    @OperaLog(operaModule = "用户启禁用",operaType = OperaLogConstant.UPDATE)
    public JsonResult userEnable(@PathVariable("enable") Short enable, @PathVariable("id") String userId) {
        return sysUserService.userEnable(enable, userId);
    }

    @SaCheckPermission("system:user:transfer")
    @PutMapping("/job-move")
    @Operation(summary = "用户调岗", description = "用户调岗")
    @ApiOperationSupport(order = 8)
    @OperaLog(operaModule = "用户调岗",operaType = OperaLogConstant.UPDATE)
    public JsonResult jobMove(@RequestBody JobMoveDto jobMoveDto) {
        return sysUserService.jobMove(jobMoveDto);
    }

    @Operation(summary = "用户拥有的菜单", description = "用户拥有的菜单")
    @ApiOperationSupport(order = 9)
    @GetMapping("/menu")
    public JsonResult userMenu() {
        return sysUserService.userMenu();
    }

    @Operation(summary = "修改密码", description = "修改密码")
    @ApiOperationSupport(order = 10)
    @PutMapping("/password")
    @OperaLog(operaModule = "修改密码",operaType = OperaLogConstant.UPDATE)
    public JsonResult password(@RequestBody PasswordDto passwordDto) {
        return sysUserService.password(passwordDto);
    }

    @Operation(summary = "上次登录时间", description = "上次登录时间")
    @ApiOperationSupport(order = 11)
    @GetMapping("/login-time")
    public JsonResult loginTime() {
        return sysUserService.loginTime();
    }

    @SaCheckPermission("system:user:restPassword")
    @Operation(summary = "重置密码", description = "重置密码")
    @ApiOperationSupport(order = 12)
    @PutMapping("/reset/password/{userId}")
    @OperaLog(operaModule = "重置密码",operaType = OperaLogConstant.UPDATE)
    public JsonResult restPassword(@PathVariable String userId) {
        return sysUserService.restPassword(userId);
    }


    @Operation(summary = "用户拥有的项目类型", description = "用户拥有的项目类型")
    @ApiOperationSupport(order = 13)
    @GetMapping("/project/classify")
    public JsonResult userProjectClassify() {
        return sysUserService.userProjectClassify();
    }

}
