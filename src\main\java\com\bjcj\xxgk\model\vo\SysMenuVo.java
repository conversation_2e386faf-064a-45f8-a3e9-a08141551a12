package com.bjcj.xxgk.model.vo;

import com.bjcj.xxgk.common.enums.MenuEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:59 周四
 */
@Schema(description = "菜单 展示对象")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SysMenuVo {

    @Schema(description = "")
    private String id;

    @Schema(description = "父菜单ID")
    private String parentId;

    @Schema(description = "组件路径")
    private String component;

    @Schema(description = "菜单类型（0:菜单，1:iframe，2:外链，3:按钮）")
    private MenuEnum menuType;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    @Schema(description = "最后操作人")
    private String operator;

    @Schema(description = "路由名称")
    private String name;

    @Schema(description = "路由路径")
    private String path;

    @Schema(description = "路由重定向")
    private String redirect;

    @Schema(description = "菜单元数据")
    private SysMenuMetaVo meta;

    private List<SysMenuVo> children;
}