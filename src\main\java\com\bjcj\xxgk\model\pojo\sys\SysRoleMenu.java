package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/20 17:34 周二
 */
@Schema(description="用户角色表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_role_menu")
public class SysRoleMenu {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private String id;

    @TableField(value = "role_id")
    @Schema(description="角色id")
    private String roleId;

    @TableField(value = "menu_id")
    @Schema(description="菜单id")
    private String menuId;
}