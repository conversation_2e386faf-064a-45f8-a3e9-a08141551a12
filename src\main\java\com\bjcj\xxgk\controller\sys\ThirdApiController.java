package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.serviceImpl.sys.ThirdApiService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/8/27 8:12
 */
@SaCheckLogin
@RestController
@RequestMapping("/third")
@Tag(name = "12三方接口")
@Validated
public class ThirdApiController {

    @Resource
    ThirdApiService thirdApiService;

    @Operation(summary = "天气", description = "天气")
    @ApiOperationSupport(order = 1)
    @GetMapping("/weather")
    public JsonResult weather() {
        return thirdApiService.dayWeather();
    }

    // @Operation(summary = "短信", description = "短信")
    // @ApiOperationSupport(order = 2)
    // @GetMapping("/message")
    // public JsonResult showTree() {
    //     return sysDepartmentService.showTree();
    // }

}
