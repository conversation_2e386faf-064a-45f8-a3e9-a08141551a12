package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.exception.CaptchaException;
import com.bjcj.xxgk.common.properties.XxgkProperties;
import com.bjcj.xxgk.common.redis.RedisHandler;
import com.bjcj.xxgk.common.redis.RedisKeyConstant;
import com.bjcj.xxgk.common.utils.SnowflakeUtil;
import com.bjcj.xxgk.common.utils.UserAgentUtil;
import com.bjcj.xxgk.model.pojo.sys.SysLoginLog;
import com.bjcj.xxgk.model.pojo.sys.SysUser;
import com.bjcj.xxgk.serviceImpl.sys.SysLoginLogService;
import com.bjcj.xxgk.serviceImpl.sys.SysUserService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/8/19 16:52 周一
 */
@RestController
@RequestMapping
@Tag(name = "01登录")
public class LoginController {

    @Resource
    RedisHandler redisHandler;

    @Resource
    XxgkProperties xxgkProperties;

    @Resource
    SysUserService sysUserService;

    @Resource
    SysLoginLogService sysLoginLogService;

    @Operation(summary = "获取验证码")
    @ApiOperationSupport(order = 1)
    @GetMapping("/captcha")
    public JsonResult getCaptcha() {

        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(120, 40, 4, 50);

        //将验证码和对应的随机key值写入缓存数据库
        String key = SnowflakeUtil.snowflakeId();
        redisHandler.set(RedisKeyConstant.CAPTCHA + key, captcha.getCode(), 90, TimeUnit.SECONDS);

        return JsonResult.success(
                MapUtil.<String, String>builder()
                        .put("key", key)
                        .put("base64Image", captcha.getImageBase64Data())
                        .build()
        );
    }

    @Operation(summary = "登录")
    @ApiOperationSupport(order = 2)
    @GetMapping("/login")
    public JsonResult doLogin(HttpServletRequest request, @RequestParam String username, @RequestParam String password,
                              @RequestParam String key, @RequestParam String captcha) {

        if (xxgkProperties.isCheckCaptcha()) {
            // 验证码校验
            String text = redisHandler.get(RedisKeyConstant.CAPTCHA + key);
            if (!StrUtil.equalsIgnoreCase(text, captcha)) {

                // 记录日志
                recordLog(request, username, "", "login", false, "验证码输入错误");
                throw new CaptchaException("验证码输入错误！");
            }
        }

        // 查询用户
        SysUser sysUser = sysUserService.getOne(
                Wrappers.<SysUser>lambdaQuery()
                        .eq(SysUser::getUsername, username)
                        .eq(SysUser::getPassword, DigestUtil.md5Hex(password))
        );
        if (ObjUtil.isNotEmpty(sysUser)) {

            // 登录
            StpUtil.login(sysUser.getId());

            // 保存用户信息
            StpUtil.getSession().set("sysUser", sysUser);

            // 记录日志
            recordLog(request, username, sysUser.getId(), "login", true, null);

            return JsonResult.success(StpUtil.getTokenInfo());
        } else {
            // 记录日志
            recordLog(request, username, "", "login", false, "账号或者密码不正确");
            return JsonResult.error("账号或者密码不正确");
        }
    }

    @Operation(summary = "登出")
    @ApiOperationSupport(order = 3)
    @GetMapping("/logout")
    public JsonResult logout(HttpServletRequest request,@RequestParam String username) {
        // 记录日志
        recordLog(request, username, String.valueOf(StpUtil.getLoginId()), "logout", true, null);
        StpUtil.logout();
        return JsonResult.success();
    }

    @Operation(summary = "查询是否登录")
    @ApiOperationSupport(order = 4)
    @GetMapping("/isLogin")
    public JsonResult isLogin() {
        return JsonResult.success("是否登录：" + StpUtil.isLogin());
    }

    @Operation(summary = "获取token信息")
    @ApiOperationSupport(order = 5)
    @GetMapping("/tokenInfo")
    public JsonResult tokenInfo() {
        return JsonResult.success(StpUtil.getTokenInfo());
    }


    @GetMapping("/list")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 6)
    @Parameters({
            @Parameter(name = "pageNum", description = "页码", required = true),
            @Parameter(name = "pageSize", description = "每页数量", required = true),
            @Parameter(name = "begintime", description = "开始时间", required = false),
            @Parameter(name = "endtime", description = "结束时间", required = false),
            @Parameter(name = "username", description = "用户名", required = false),
            @Parameter(name = "loginOrOut", description = "登录/登出", required = false),
            @Parameter(name = "status", description = "状态", required = false),
    })
    public JsonResult list(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize,
                           @RequestParam(value = "begintime", required = false) String begintime,
                           @RequestParam(value = "endtime", required = false) String endtime,
                           @RequestParam(value = "username", required = false) String username,
                           @RequestParam(value = "loginOrOut", required = false) String loginOrOut,
                           @RequestParam(value = "status", required = false) Boolean status) {

        // 创建基础查询条件
        LambdaQueryWrapper<SysLoginLog> queryWrapper = Wrappers.<SysLoginLog>lambdaQuery()
                .eq(StrUtil.isNotBlank(username), SysLoginLog::getUsername, username)
                .eq(StrUtil.isNotBlank(loginOrOut), SysLoginLog::getLoginOrOut, loginOrOut)
                .eq(ObjUtil.isNotEmpty(status), SysLoginLog::getStatus, status)
                .orderByDesc(SysLoginLog::getCreateTime);

        // 安全处理时间范围
        boolean hasTimeRange = StrUtil.isNotBlank(begintime) || StrUtil.isNotBlank(endtime);
        if (hasTimeRange) {
            LocalDateTime start = LocalDateTime.parse(begintime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end = LocalDateTime.parse(endtime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (start != null || end != null) {
                queryWrapper.between(
                        SysLoginLog::getCreateTime,
                        start != null ? start : LocalDateTime.MIN, // 若无开始时间取最小值
                        end != null ? end : LocalDateTime.MAX     // 若无结束时间取最大值
                );
            }
        }

        Page<SysLoginLog> page = sysLoginLogService.page(new Page<>(pageNum, pageSize), queryWrapper);
        return JsonResult.success(page);
    }



    private void recordLog(HttpServletRequest request, String username, String userId,
                           String loginOrOut, boolean status, String failMsg) {
        // 记录日志
        sysLoginLogService.save(
                SysLoginLog.builder()
                        .username(username)
                        .userId(userId)
                        .createTime(LocalDateTime.now())
                        .ip(JakartaServletUtil.getClientIP(request))
                        .device(UserAgentUtil.getDevice(request))
                        .loginOrOut(loginOrOut)
                        .status(status)
                        .failedMsg(failMsg)
                        .build()
        );
    }


}
