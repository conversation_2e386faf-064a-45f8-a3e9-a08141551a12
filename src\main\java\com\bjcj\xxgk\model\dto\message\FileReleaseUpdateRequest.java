package com.bjcj.xxgk.model.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 文件发布更新请求DTO
 * <AUTHOR>
 * @date 2024/12/25
 */
@Schema(description = "文件发布更新请求")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FileReleaseUpdateRequest {

    @Size(max = 255, message = "实例ID长度不能超过255个字符")
    @Schema(description = "实例ID")
    private String instanceId;

    @Size(max = 255, message = "新闻/消息类型长度不能超过255个字符")
    @Schema(description = "新闻/消息类型")
    private String newsType;

    @Size(max = 255, message = "文件/消息标题长度不能超过255个字符")
    @Schema(description = "文件/消息标题")
    private String fileHeader;

    @Schema(description = "发布时间")
    private LocalDateTime releaseTime;

    @Size(max = 255, message = "发布者/作者长度不能超过255个字符")
    @Schema(description = "发布者/作者")
    private String releaseUser;

    @Size(max = 255, message = "发布状态长度不能超过255个字符")
    @Schema(description = "发布状态")
    private String status;

    @Schema(description = "详细内容")
    private String details;
}
