package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/8/29 16:15 周四
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description="字典数据表")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dict")
public class SysDict extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="id")
    private String id;

    @TableField(value = "sort")
    @Schema(description="字典排序")
    private Short sort;

    @TableField(value = "\"label\"")
    @Schema(description="字典标签")
    private String label;

    @TableField(value = "\"value\"")
    @Schema(description="字典键值")
    private String value;

    @NotBlank(message = "字典代码不能为空")
    @TableField(value = "code")
    @Schema(description="字典代码")
    private String code;

    @TableField(value = "is_default")
    @Schema(description="是否默认（Y是 N否）")
    private Boolean isDefault;

    @TableField(value = "\"status\"")
    @Schema(description="状态（0停用 1正常）")
    private Short status;

    @TableField(value = "parent_id")
    @Schema(description="父级id,级联才会有")
    private String parentId;

    @TableField(value = "group_name")
    @Schema(description="组名称，分组才会有")
    private String groupName;

    @TableField(value = "remark")
    @Schema(description="备注")
    private String remark;

    @TableField(value = "color")
    @Schema(description="颜色")
    private String color;

}