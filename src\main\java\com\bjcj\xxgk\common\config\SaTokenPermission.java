package com.bjcj.xxgk.common.config;

import cn.dev33.satoken.stp.StpInterface;
import cn.hutool.core.util.ObjUtil;
import com.bjcj.xxgk.mapper.sys.SysMenuMapper;
import com.bjcj.xxgk.mapper.sys.SysRoleMapper;
import com.bjcj.xxgk.model.pojo.sys.SysMenu;
import com.bjcj.xxgk.model.pojo.sys.SysRole;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Optional;

/**
 * 自定义权限加载接口实现类
 *
 * <AUTHOR>
 * @date 2024/8/20 16:35 周二
 */
@Configuration
public class SaTokenPermission implements StpInterface {

    @Resource
    SysMenuMapper sysMenuMapper;

    @Resource
    SysRoleMapper sysRoleMapper;

    /**
     * 返回一个账号所拥有的权限码集合
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {

        return Optional.ofNullable(sysMenuMapper.getPermissionByUserId((String) loginId))
                .orElse(Lists.newArrayList())
                .stream()
                .filter(ObjUtil::isNotEmpty)
                .filter(SysMenu::getShowLink)
                .map(SysMenu::getAuths)
                .toList();
    }

    /**
     * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        return sysRoleMapper.getRoleByUserId((String) loginId)
                .stream()
                .map(SysRole::getCode)
                .toList();
    }

}
