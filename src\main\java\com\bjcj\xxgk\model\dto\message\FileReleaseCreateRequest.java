package com.bjcj.xxgk.model.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 文件发布创建请求DTO
 * 
 * <AUTHOR>
 * @date 2024/12/25
 */
@Schema(description = "文件发布创建请求")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FileReleaseCreateRequest {

    @NotBlank(message = "实例ID不能为空")
    @Size(max = 255, message = "实例ID长度不能超过255个字符")
    @Schema(description = "实例ID", required = true)
    private String instanceId;

    @NotBlank(message = "新闻/消息类型不能为空")
    @Size(max = 255, message = "新闻/消息类型长度不能超过255个字符")
    @Schema(description = "新闻/消息类型", required = true)
    private String newsType;

    @NotBlank(message = "文件/消息标题不能为空")
    @Size(max = 255, message = "文件/消息标题长度不能超过255个字符")
    @Schema(description = "文件/消息标题", required = true)
    private String fileHeader;

    @Schema(description = "发布时间")
    private LocalDateTime releaseTime;

    @NotBlank(message = "发布者/作者不能为空")
    @Size(max = 255, message = "发布者/作者长度不能超过255个字符")
    @Schema(description = "发布者/作者", required = true)
    private String releaseUser;

    @NotBlank(message = "发布状态不能为空")
    @Size(max = 255, message = "发布状态长度不能超过255个字符")
    @Schema(description = "发布状态", required = true)
    private String status;

    @Schema(description = "详细内容")
    private String details;
}
