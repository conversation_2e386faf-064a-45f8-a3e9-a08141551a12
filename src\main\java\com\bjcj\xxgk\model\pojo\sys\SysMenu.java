package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.bjcj.xxgk.common.enums.MenuEnum;
import com.bjcj.xxgk.model.vo.SysMenuMetaTransitionVo;
import com.bjcj.xxgk.model.vo.SysMenuMetaVo;
import com.bjcj.xxgk.model.vo.SysMenuVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:59 周四
 */
@Schema(description = "菜单表")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_menu")
public class SysMenu {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    @TableField(value = "title")
    @Schema(description = "菜单名称")
    private String title;

    @TableField(value = "parent_id")
    @Schema(description = "父菜单ID")
    private String parentId;

    @TableField(value = "\"rank\"")
    @Schema(description = "菜单排序")
    private Short rank;

    @TableField(value = "component")
    @Schema(description = "组件路径")
    private String component;

    @TableField(value = "menu_type")
    @Schema(description = "菜单类型（0:菜单，1:iframe，2:外链，3:按钮）")
    private MenuEnum menuType;

    @TableField(value = "icon")
    @Schema(description = "菜单图标")
    private String icon;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField(value = "\"operator\"", fill = FieldFill.INSERT)
    @Schema(description = "最后操作人")
    private String operator;

    @TableField(value = "\"name\"")
    @Schema(description = "路由名称")
    private String name;

    @TableField(value = "\"path\"")
    @Schema(description = "路由路径")
    private String path;

    @TableField(value = "redirect")
    @Schema(description = "路由重定向")
    private String redirect;

    @TableField(value = "extra_icon")
    @Schema(description = "菜单右侧图标")
    private String extraIcon;

    @TableField(value = "enter_transition")
    @Schema(description = "进场动画")
    private String enterTransition;

    @TableField(value = "leave_transition")
    @Schema(description = "离场动画")
    private String leaveTransition;

    @TableField(value = "active_path")
    @Schema(description = "菜单激活")
    private String activePath;

    @TableField(value = "auths")
    @Schema(description = "类型为按钮时的权限标识")
    private String auths;

    @TableField(value = "frame_src")
    @Schema(description = "iframe 链接地址")
    private String frameSrc;

    @TableField(value = "frame_loading")
    @Schema(description = "是否显示iframe加载动画")
    private Boolean frameLoading;

    @TableField(value = "show_link")
    @Schema(description = "是否显示菜单")
    private Boolean showLink;

    @TableField(value = "show_parent")
    @Schema(description = "是否显示父级菜单")
    private Boolean showParent;

    @TableField(value = "keep_alive")
    @Schema(description = "是否缓存页面")
    private Boolean keepAlive;

    @TableField(value = "hidden_tag")
    @Schema(description = "是否允许加载到标签页")
    private Boolean hiddenTag;

    @TableField(value = "fixed_tag")
    @Schema(description = "是否固定标签页")
    private Boolean fixedTag;


    public SysMenuVo toSysMenuVo() {
        return SysMenuVo.builder()
                .id(id)
                .parentId(parentId)
                .component(component)
                .menuType(menuType)
                .createTime(createTime)
                .updateTime(updateTime)
                .operator(operator)
                .name(name)
                .path(path)
                .redirect(redirect)
                .meta(
                        SysMenuMetaVo.builder()
                                .title(title)
                                .rank(rank)
                                .icon(icon)
                                .extraIcon(extraIcon)
                                .activePath(activePath)
                                .auths(auths)
                                .frameSrc(frameSrc)
                                .frameLoading(frameLoading)
                                .showLink(showLink)
                                .showParent(showParent)
                                .keepAlive(keepAlive)
                                .hiddenTag(hiddenTag)
                                .fixedTag(fixedTag)
                                .transition(
                                        SysMenuMetaTransitionVo.builder()
                                                .enterTransition(enterTransition)
                                                .leaveTransition(leaveTransition)
                                                .build()
                                )
                                .build()
                )
                .build();
    }
}