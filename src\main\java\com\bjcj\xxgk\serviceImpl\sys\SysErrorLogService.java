package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.dto.sys.SysErrorLogDto;
import com.bjcj.xxgk.mapper.sys.SysErrorLogMapper;
import com.bjcj.xxgk.model.pojo.sys.SysErrorLog;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/4 16:05 周一
 */
@Service
public class SysErrorLogService extends ServiceImpl<SysErrorLogMapper, SysErrorLog> {


    public JsonResult errorLogList(SysErrorLogDto sysErrorLogDto) {
        Page<SysErrorLog> page = page(Page.of(sysErrorLogDto.getCurrent(), sysErrorLogDto.getSize()),
                Wrappers.<SysErrorLog>query().lambda()
                        .eq(StrUtil.isNotBlank(sysErrorLogDto.getErrorName()), SysErrorLog::getName, sysErrorLogDto.getErrorName())
                        .like(
                                StrUtil.isNotBlank(sysErrorLogDto.getUsername()),
                                SysErrorLog::getUserName,
                                StrUtil.isNotBlank(sysErrorLogDto.getUsername()) ? sysErrorLogDto.getUsername().toLowerCase() : null
                        )
                        .like(StrUtil.isNotBlank(sysErrorLogDto.getIp()), SysErrorLog::getIp, sysErrorLogDto.getIp())
                        .like(
                                StrUtil.isNotBlank(sysErrorLogDto.getType()),
                                SysErrorLog::getType,
                                StrUtil.isNotBlank(sysErrorLogDto.getType()) ? sysErrorLogDto.getType().toUpperCase() : null
                        )
                        .ge(ObjUtil.isNotEmpty(sysErrorLogDto.getStartTime()), SysErrorLog::getCreateTime, sysErrorLogDto.getStartTime())
                        .le(ObjUtil.isNotEmpty(sysErrorLogDto.getEndTime()), SysErrorLog::getCreateTime, sysErrorLogDto.getEndTime())
                        .orderByDesc(SysErrorLog::getCreateTime)
        );
        return JsonResult.success(page);
    }
}
