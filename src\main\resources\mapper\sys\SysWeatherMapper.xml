<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysWeatherMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysWeather">
        <!--@mbg.generated-->
        <!--@Table sys_weather-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="day" jdbcType="TIMESTAMP" property="day"/>
        <result column="json" jdbcType="VARCHAR" property="json"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, "day", json
    </sql>
</mapper>