package com.bjcj.xxgk.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型
 * <AUTHOR>
 * @date 2024/8/20 18:03 周二
 */
@Getter
@AllArgsConstructor
public enum ProjectStatusEnum {

    /**
     * 入库
     */
    PUT(0),

    /**
     * 成立
     */
    SETUP(1),

    /**
     * 退回
     */
    RETURN(2),

    ;

    @EnumValue
    @JsonValue
    private final Integer code;
}
