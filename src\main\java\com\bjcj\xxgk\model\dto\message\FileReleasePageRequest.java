package com.bjcj.xxgk.model.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 文件发布分页查询请求DTO
 * 
 * <AUTHOR>
 * @date 2024/12/25
 */
@Schema(description = "文件发布分页查询请求")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class FileReleasePageRequest {

    @NotNull(message = "分页current不允许为空")
    @Positive(message = "当前页必须大于0")
    @Schema(description = "当前页", required = true, defaultValue = "1")
    @Builder.Default
    private Long current = 1L;

    @NotNull(message = "分页size不允许为空")
    @Positive(message = "每页数量必须大于0")
    @Schema(description = "每页数量", required = true, defaultValue = "10")
    @Builder.Default
    private Long size = 10L;

    @Schema(description = "实例ID")
    private String instanceId;

    @Schema(description = "新闻/消息类型")
    private String newsType;

    @Schema(description = "文件/消息标题")
    private String fileHeader;

    @Schema(description = "发布者/作者")
    private String releaseUser;

    @Schema(description = "发布状态")
    private String status;

    @Schema(description = "发布开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime releaseStartTime;

    @Schema(description = "发布结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime releaseEndTime;

    @Schema(description = "创建开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createStartTime;

    @Schema(description = "创建结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createEndTime;
}
