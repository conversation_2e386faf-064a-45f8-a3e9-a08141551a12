<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysRoleMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysRole">
        <!--@Table sys_role-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="is_bear" jdbcType="BOOLEAN" property="isBear"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, "name", code, sort, create_time, update_time, "status", "operator", is_bear, remark
    </sql>

    <select id="getRoleByUserId" resultMap="BaseResultMap">
        select sr.*
        from sys_role sr
        left join sys_user_role sur on sr.id = sur.role_id
        <where>
            <if test="userId != null and userId != ''">
                sur.user_id = #{userId}
            </if>
        </where>
    </select>
</mapper>