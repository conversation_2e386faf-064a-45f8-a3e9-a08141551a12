package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.constant.XxgkConstant;
import com.bjcj.xxgk.common.domain.Code;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.utils.SnowflakeUtil;
import com.bjcj.xxgk.mapper.sys.SysMenuMapper;
import com.bjcj.xxgk.mapper.sys.SysRoleMenuMapper;
import com.bjcj.xxgk.model.dto.sys.SysMenuDto;
import com.bjcj.xxgk.model.pojo.sys.SysMenu;
import com.bjcj.xxgk.model.pojo.sys.SysRoleMenu;
import com.bjcj.xxgk.model.vo.SysMenuVo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/20 17:34 周二
 */
@Service
public class SysMenuService extends ServiceImpl<SysMenuMapper, SysMenu> {

    @Resource
    SysRoleMenuMapper sysRoleMenuMapper;

    public JsonResult del(String id) {
        // 1.检查有没有子级菜单
        List<SysMenu> children = list(Wrappers.<SysMenu>lambdaQuery().eq(SysMenu::getParentId, id));
        if (CollUtil.isNotEmpty(children)) {
            return JsonResult.build(Code.DELETE_ERROR.value(), "该菜单下有子级菜单，不允许删除");
        }
        // 2.删除
        removeById(id);
        // 3.删除关联表的信息
        sysRoleMenuMapper.delete(
                Wrappers.<SysRoleMenu>lambdaQuery()
                        .eq(SysRoleMenu::getMenuId, id)
        );
        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult add(SysMenu sysMenu) {
        if (StrUtil.isNotBlank(sysMenu.getId())) {
            return JsonResult.error("新增接口，不允许传id");
        }
        String id = SnowflakeUtil.snowflakeId();
        // 新加的菜单和权限默认给超管一份
        sysRoleMenuMapper.insert(
                SysRoleMenu.builder().roleId(XxgkConstant.SUPER_ADMIN_ID).menuId(id).build()
        );
        sysMenu.setId(id);
        return save(sysMenu) ? JsonResult.success() : JsonResult.error();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult fix(SysMenu sysMenu) {
        if (StrUtil.isBlank(sysMenu.getId())) {
            return JsonResult.error("id是必传的");
        }
        return update(sysMenu, Wrappers.<SysMenu>lambdaQuery().eq(SysMenu::getId, sysMenu.getId())) ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult tree() {

        // 1. 查询全部菜单
        List<SysMenuDto> allMenus = list(Wrappers.<SysMenu>lambdaQuery().orderByAsc(SysMenu::getRank)).stream()
                .map(item -> BeanUtil.copyProperties(item, SysMenuDto.class))
                .toList();
        // 2. 构建树 并返回
        return JsonResult.success(buildMenuTree(allMenus));
    }

    /**
     * <h2>buildMenuTree</h2>
     * @param allMenus:
     * @return java.util.List<com.bjcj.kpi.model.dto.sys.SysMenuDto>
     * <AUTHOR>
     * @date 2024/9/4 17:56
     */
    public List<SysMenuDto> buildMenuTree(List<SysMenuDto> allMenus) {
        if (CollUtil.isEmpty(allMenus)) {
            return Collections.emptyList();
        }
        return allMenus.stream()
                .filter(item -> StrUtil.isBlank(item.getParentId()))
                .peek(item -> {
                    item.setChildren(getChildren(item, allMenus));
                })
                .collect(Collectors.toList());
    }

    public List<SysMenuVo> buildMenuTree1(List<SysMenuVo> allMenus) {
        if (CollUtil.isEmpty(allMenus)) {
            return Collections.emptyList();
        }
        return allMenus.stream()
                .filter(item -> StrUtil.isBlank(item.getParentId()))
                .peek(item -> {
                    item.setChildren(getChildren(item, allMenus));
                })
                .collect(Collectors.toList());
    }

    //==============================================private==========================================

    private List<SysMenuDto> getChildren(SysMenuDto root, List<SysMenuDto> all) {
        return all.stream()
                .filter(item -> Objects.equals(item.getParentId(), root.getId()))
                .peek(item -> item.setChildren(getChildren(item, all)))
                .sorted(Comparator.comparing(SysMenuDto::getRank))
                .collect(Collectors.toList());
    }

    private List<SysMenuVo> getChildren(SysMenuVo root, List<SysMenuVo> all) {
        return all.stream()
                .filter(item -> Objects.equals(item.getParentId(), root.getId()))
                .peek(item -> item.setChildren(getChildren(item, all)))
                .sorted(Comparator.comparing(i -> i.getMeta().getRank()))
                .collect(Collectors.toList());
    }


}
