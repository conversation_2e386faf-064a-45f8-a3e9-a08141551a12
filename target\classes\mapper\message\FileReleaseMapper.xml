<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.message.FileReleaseMapper">
    
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.message.FileRelease">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="instance_id" jdbcType="VARCHAR" property="instanceId"/>
        <result column="news_type" jdbcType="VARCHAR" property="newsType"/>
        <result column="file_header" jdbcType="VARCHAR" property="fileHeader"/>
        <result column="release_time" jdbcType="TIMESTAMP" property="releaseTime"/>
        <result column="release_user" jdbcType="VARCHAR" property="releaseUser"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="details" jdbcType="VARCHAR" property="details"/>
    </resultMap>

    <resultMap id="ResponseResultMap" type="com.bjcj.xxgk.model.dto.message.FileReleaseResponse">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="instance_id" jdbcType="VARCHAR" property="instanceId"/>
        <result column="news_type" jdbcType="VARCHAR" property="newsType"/>
        <result column="file_header" jdbcType="VARCHAR" property="fileHeader"/>
        <result column="release_time" jdbcType="TIMESTAMP" property="releaseTime"/>
        <result column="release_user" jdbcType="VARCHAR" property="releaseUser"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="details" jdbcType="VARCHAR" property="details"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, instance_id, news_type, file_header, release_time, release_user, status, create_time, update_time, details
    </sql>

    <select id="selectFileReleasePage" resultMap="ResponseResultMap">
        select 
        <include refid="Base_Column_List"/>
        from file_release
        <where>
            <if test="request.instanceId != null and request.instanceId != ''">
                and instance_id = #{request.instanceId}
            </if>
            <if test="request.newsType != null and request.newsType != ''">
                and news_type = #{request.newsType}
            </if>
            <if test="request.fileHeader != null and request.fileHeader != ''">
                and file_header like concat('%', #{request.fileHeader}, '%')
            </if>
            <if test="request.releaseUser != null and request.releaseUser != ''">
                and release_user like concat('%', #{request.releaseUser}, '%')
            </if>
            <if test="request.status != null and request.status != ''">
                and status = #{request.status}
            </if>
            <if test="request.releaseStartTime != null">
                and release_time >= #{request.releaseStartTime}
            </if>
            <if test="request.releaseEndTime != null">
                and release_time &lt;= #{request.releaseEndTime}
            </if>
            <if test="request.createStartTime != null">
                and create_time >= #{request.createStartTime}
            </if>
            <if test="request.createEndTime != null">
                and create_time &lt;= #{request.createEndTime}
            </if>
        </where>
        order by release_time desc, create_time desc
    </select>

</mapper>
