package com.bjcj.xxgk.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 目录类型
 * <AUTHOR>
 * @date 2024/8/20 18:03 周二
 */
@Getter
@AllArgsConstructor
public enum DictEnum {


    /**
     * 普通
     */
    NORMAL(1),

    /**
     * 级联
     */
    CASCADE(2),

    /**
     * 分组
     */
    GROUP(3);

    @EnumValue
    @JsonValue
    private final Integer code;
}
