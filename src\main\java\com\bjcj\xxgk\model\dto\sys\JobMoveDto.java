package com.bjcj.xxgk.model.dto.sys;

import com.bjcj.xxgk.model.pojo.sys.SysUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/9/5 10:27 周四
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class JobMoveDto extends SysUser {

    private String userId;

    private String departmentId;
    private String oldDepartmentId;

    private String jobId;
    private String oldJobId;

}
