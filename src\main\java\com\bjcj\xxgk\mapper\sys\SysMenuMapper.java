package com.bjcj.xxgk.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.xxgk.common.enums.MenuEnum;
import com.bjcj.xxgk.model.pojo.sys.SysMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:59 周四
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    List<SysMenu> getMenuByUserIdAndMenuType(@Param("userId") String userId, @Param("menuType") MenuEnum menuType);
    List<SysMenu> getPermissionByUserId(@Param("userId") String userId);
    List<SysMenu> getMenuByUserId(@Param("userId") String userId);

    List<SysMenu> getMenuByRoleId(@Param("roleId") String roleId);
}