package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.common.constant.XxgkConstant;
import com.bjcj.xxgk.common.constant.OperaLogConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.dto.sys.SysJobDto;
import com.bjcj.xxgk.model.pojo.sys.SysJob;
import com.bjcj.xxgk.serviceImpl.sys.SysJobService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/8/20 18:36
 */
@SaCheckLogin
@RestController
@RequestMapping("/job")
@Tag(name = "04职务")
@Validated
public class JobController {
    @Resource
    private SysJobService sysJobService;

    @Operation(summary = "职务分页列表", description = "职务分页列表")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult jobPage(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
        return JsonResult.success(sysJobService.page(
                new Page<>(pageNum, pageSize),
                Wrappers.<SysJob>lambdaQuery().orderByAsc(SysJob::getSort).orderByDesc(SysJob::getCreateTime)
        ));
    }

    @Operation(summary = "展示页面职务列表", description = "展示页面职务列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/show")
    public JsonResult showList() {
        return JsonResult.success(sysJobService.list(
                Wrappers.<SysJob>lambdaQuery()
                        .eq(SysJob::getStatus, XxgkConstant.ENABLE)
                        .orderByAsc(SysJob::getSort).orderByDesc(SysJob::getCreateTime)
        ));
    }

    @Operation(summary = "职务权重", description = "职务权重")
    @ApiOperationSupport(order = 2)
    @GetMapping("/{id}")
    public JsonResult jobWeight(@PathVariable("id") String id) {
        return sysJobService.jobWeight(id);
    }

    @PostMapping
    @Operation(summary = "新增职务", description = "新增职务")
    @ApiOperationSupport(order = 3)
    @OperaLog(operaModule = "新增职务",operaType = OperaLogConstant.CREATE)
    public JsonResult add(@Validated @RequestBody SysJobDto sysJobDto) {
        return sysJobService.add(sysJobDto);
    }

    @SaCheckPermission("system:duty:edit")
    @PutMapping
    @Operation(summary = "编辑职务", description = "编辑职务")
    @ApiOperationSupport(order = 4)
    @OperaLog(operaModule = "编辑职务",operaType = OperaLogConstant.UPDATE)
    public JsonResult fix(@Validated @RequestBody SysJobDto sysJobDto) {
        return sysJobService.fix(sysJobDto);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除职务", description = "删除职务")
    @ApiOperationSupport(order = 5)
    @OperaLog(operaModule = "删除职务",operaType = OperaLogConstant.DELETE)
    public JsonResult delete(@PathVariable("id") String id) {
        return sysJobService.del(id);
    }

}
