package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-09-29 12:01 周日
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_user_branch_dep")
public class SysUserBranchDep {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private String id;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    @Schema(description="用户id")
    private String userId;

    /**
     * 分管的部门id
     */
    @TableField(value = "department_id")
    @Schema(description="分管的部门id")
    private String departmentId;
}