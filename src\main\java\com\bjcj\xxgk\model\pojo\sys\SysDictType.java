package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.bjcj.xxgk.common.enums.DictEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/29 16:15 周四
 */
@Schema(description="字典类型表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_dict_type")
public class SysDictType {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="字典主键")
    private String id;

    @TableField(value = "\"name\"")
    @Schema(description="字典名称")
    private String name;

    @TableField(value = "code")
    @Schema(description="字典代码")
    private String code;

    @TableField(value = "\"status\"")
    @Schema(description="状态（0停用 1正常）")
    private Short status;

    @TableField(value = "\"operator\"")
    @Schema(description="最后操作人")
    private String operator;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField(value = "remark")
    @Schema(description="备注")
    private String remark;

    @TableField(value = "\"type\"")
    @Schema(description="字典类型(1普通,2级联,3分组)")
    private DictEnum type;
}