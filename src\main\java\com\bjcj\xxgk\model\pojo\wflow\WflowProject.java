package com.bjcj.xxgk.model.pojo.wflow;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value="wflow_project")
public class WflowProject {
    /**
    * 主键ID
    */
    @Schema(description="主键ID")
    private String id;

    /**
    * 实例ID
    */
    @Schema(description="实例ID")
    private String instanceId;

    /**
    * 流程名称
    */
    @Schema(description="流程名称")
    private String processDefName;

    /**
    * 实例名称
    */
    @Schema(description="实例名称")
    private String instanceName;

    /**
    * 状态
    */
    @Schema(description="状态")
    private String status;

    /**
    * 发起人ID
    */
    @Schema(description="发起人ID")
    private String startUserId;

    /**
    * 发起人名称
    */
    @Schema(description="发起人名称")
    private String startUserName;

    /**
    * 发起人部门
    */
    @Schema(description="发起人部门")
    private String startUserDept;

    /**
    * 表单值
    */
    @Schema(description="表单值")
    private String formData;

    /**
    * 目录
    */
    @Schema(description="目录")
    private String formDir;

    /**
    * 流程进度
    */
    @Schema(description="流程进度")
    private String progress;

    /**
    * 开始时间
    */
    @Schema(description="开始时间")
    private Date startTime;

    /**
    * 备注
    */
    @Schema(description="备注")
    private String remark;

}