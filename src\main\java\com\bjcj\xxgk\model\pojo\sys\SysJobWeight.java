package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024-09-30 17:06 周一
 */
@Schema(description = "职务权重表")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_job_weight")
public class SysJobWeight {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    /**
     * 职务id
     */
    @TableField(value = "job_id")
    @Schema(description = "职务id")
    private String jobId;

    /**
     * 评委id
     */
    @TableField(value = "judge")
    @Schema(description = "评委id")
    private String judge;

    /**
     * 权重
     */
    @TableField(value = "weight")
    @Schema(description = "权重")
    private String weight;

    /**
     * 评委真实id
     */
    @TableField(value = "judge1")
    @Schema(description = "评委真实id")
    private String judge1;

    /**
     * 数量
     */
    @TableField(value = "amount")
    @Schema(description = "数量")
    private String amount;
}