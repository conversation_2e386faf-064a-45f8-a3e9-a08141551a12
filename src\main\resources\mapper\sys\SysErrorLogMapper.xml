<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysErrorLogMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysErrorLog">
    <!--@Table public.sys_error_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="req_param" jdbcType="VARCHAR" property="reqParam" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="uri" jdbcType="VARCHAR" property="uri" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, req_param, "name", message, user_id, user_name, "method", uri, ip, create_time
  </sql>
</mapper>