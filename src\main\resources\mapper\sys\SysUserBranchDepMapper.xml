<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysUserBranchDepMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysUserBranchDep">
        <!--@mbg.generated-->
        <!--@Table sys_user_branch_dep-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="department_id" jdbcType="VARCHAR" property="departmentId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, department_id
    </sql>

    <select id="getUserBranchDep" resultType="com.bjcj.xxgk.model.pojo.sys.SysDepartment">
        select sd.*
        from sys_department sd
        left join sys_user_branch_dep subd on sd.id = subd.department_id
        where subd.user_id = #{userId}
    </select>
</mapper>