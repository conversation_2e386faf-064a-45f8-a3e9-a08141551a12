package com.bjcj.xxgk.mapper.sys;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2024-09-23 17:33 周一
 */
public interface ConstraintMapper {

    // 查询约束的注释信息
    @Select("SELECT d.description\n" +
            "FROM pg_description d\n" +
            "JOIN (\n" +
            "    SELECT indexrelid\n" +
            "    FROM pg_index i\n" +
            "    JOIN pg_class c ON i.indrelid = c.oid\n" +
            "    WHERE i.indexrelid::regclass::text = #{constraintName}\n" +
            ") AS sub ON d.objoid = sub.indexrelid")
    String getConstraintComment(@Param("constraintName") String constraintName);

}