package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.dto.sys.SysOperaLogDto;
import com.bjcj.xxgk.mapper.sys.SysOperaLogMapper;
import com.bjcj.xxgk.model.pojo.sys.SysOperaLog;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/4 16:05 周一
 */
@Service
public class SysOperaLogService extends ServiceImpl<SysOperaLogMapper, SysOperaLog> {


    /**
     * <h2>获取操作日志列表</h2>
     * @param sysOperaLogQo:
     * @return com.bjcj.common.core.domain.JsonResult<java.util.List<com.bjcj.model.po.system.SysOperaLog>>
     * <AUTHOR>
     * @date 2023/12/19 17:54
     */
    public JsonResult operaLogList(SysOperaLogDto sysOperaLogQo) {
        if(ObjUtil.isNotEmpty(sysOperaLogQo.getTakeUpTime())){
            Page<SysOperaLog> page = page(Page.of(sysOperaLogQo.getCurrent(), sysOperaLogQo.getSize()),
                    Wrappers.<SysOperaLog>query().lambda()
                            .like(StrUtil.isNotBlank(sysOperaLogQo.getModule()), SysOperaLog::getModule, sysOperaLogQo.getModule())
                            .like(StrUtil.isNotBlank(sysOperaLogQo.getType()), SysOperaLog::getType, sysOperaLogQo.getType())
                            .like(
                                    StrUtil.isNotBlank(sysOperaLogQo.getReqType()),
                                    SysOperaLog::getReqType,
                                    StrUtil.isNotBlank(sysOperaLogQo.getReqType()) ? sysOperaLogQo.getReqType().toUpperCase() : null
                            )
                            .like(
                                    StrUtil.isNotBlank(sysOperaLogQo.getUsername()),
                                    SysOperaLog::getUserName,
                                    StrUtil.isNotBlank(sysOperaLogQo.getUsername()) ? sysOperaLogQo.getUsername().toLowerCase() : null
                            )
                            .like(StrUtil.isNotBlank(sysOperaLogQo.getIp()), SysOperaLog::getIp, sysOperaLogQo.getIp())
                            .ge(ObjUtil.isNotEmpty(sysOperaLogQo.getTakeUpTime()), SysOperaLog::getTakeUpTime, sysOperaLogQo.getTakeUpTime())
                            .ge(ObjUtil.isNotEmpty(sysOperaLogQo.getStartTime()), SysOperaLog::getCreateTime, sysOperaLogQo.getStartTime())
                            .le(ObjUtil.isNotEmpty(sysOperaLogQo.getEndTime()), SysOperaLog::getCreateTime, sysOperaLogQo.getEndTime())
                            .orderByAsc(SysOperaLog::getTakeUpTime)
            );
            return JsonResult.success(page);
        }
        Page<SysOperaLog> page = page(Page.of(sysOperaLogQo.getCurrent(), sysOperaLogQo.getSize()),
                Wrappers.<SysOperaLog>query().lambda()
                        .like(StrUtil.isNotBlank(sysOperaLogQo.getModule()), SysOperaLog::getModule, sysOperaLogQo.getModule())
                        .like(StrUtil.isNotBlank(sysOperaLogQo.getType()), SysOperaLog::getType, sysOperaLogQo.getType())
                        .like(
                                StrUtil.isNotBlank(sysOperaLogQo.getReqType()),
                                SysOperaLog::getReqType,
                                StrUtil.isNotBlank(sysOperaLogQo.getReqType()) ? sysOperaLogQo.getReqType().toUpperCase() : null
                        )
                        .like(
                                StrUtil.isNotBlank(sysOperaLogQo.getUsername()),
                                SysOperaLog::getUserName,
                                StrUtil.isNotBlank(sysOperaLogQo.getUsername()) ? sysOperaLogQo.getUsername().toLowerCase() : null
                        )
                        .like(StrUtil.isNotBlank(sysOperaLogQo.getIp()), SysOperaLog::getIp, sysOperaLogQo.getIp())
                        .ge(ObjUtil.isNotEmpty(sysOperaLogQo.getTakeUpTime()), SysOperaLog::getTakeUpTime, sysOperaLogQo.getTakeUpTime())
                        .ge(ObjUtil.isNotEmpty(sysOperaLogQo.getStartTime()), SysOperaLog::getCreateTime, sysOperaLogQo.getStartTime())
                        .le(ObjUtil.isNotEmpty(sysOperaLogQo.getEndTime()), SysOperaLog::getCreateTime, sysOperaLogQo.getEndTime())
                        .orderByDesc(SysOperaLog::getCreateTime)
        );

        return JsonResult.success(page);
    }
}
