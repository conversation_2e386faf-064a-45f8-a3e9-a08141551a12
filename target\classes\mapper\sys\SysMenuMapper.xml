<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysMenuMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysMenu">
        <!--@Table sys_menu-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="rank" jdbcType="SMALLINT" property="rank"/>
        <result column="component" jdbcType="VARCHAR" property="component"/>
        <result column="menu_type" jdbcType="SMALLINT" property="menuType"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="redirect" jdbcType="VARCHAR" property="redirect"/>
        <result column="extra_icon" jdbcType="VARCHAR" property="extraIcon"/>
        <result column="enter_transition" jdbcType="VARCHAR" property="enterTransition"/>
        <result column="leave_transition" jdbcType="VARCHAR" property="leaveTransition"/>
        <result column="active_path" jdbcType="VARCHAR" property="activePath"/>
        <result column="auths" jdbcType="VARCHAR" property="auths"/>
        <result column="frame_src" jdbcType="VARCHAR" property="frameSrc"/>
        <result column="frame_loading" jdbcType="BOOLEAN" property="frameLoading"/>
        <result column="show_link" jdbcType="BOOLEAN" property="showLink"/>
        <result column="show_parent" jdbcType="BOOLEAN" property="showParent"/>
        <result column="keep_alive" jdbcType="BOOLEAN" property="keepAlive"/>
        <result column="hidden_tag" jdbcType="BOOLEAN" property="hiddenTag"/>
        <result column="fixed_tag" jdbcType="BOOLEAN" property="fixedTag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, title, parent_id, "rank", component, menu_type, icon, create_time, update_time,
        "operator", "name", "path", redirect, extra_icon, enter_transition, leave_transition,
        active_path, auths, frame_src, frame_loading, show_link, show_parent, keep_alive,
        hidden_tag, fixed_tag
    </sql>

    <select id="getMenuByUserIdAndMenuType" resultMap="BaseResultMap">
        select sm.*
        from sys_user_role sur
        left join sys_role_menu srm on srm.role_id = sur.role_id
        left join sys_menu      sm on sm.id = srm.menu_id
        <where>
            <if test="userId != null and userId != ''">
                sur.user_id = #{userId}
            </if>
            <if test="menuType != null">
                and sm.menu_type = #{menuType.code}
            </if>
        </where>
        order by sm.rank
    </select>

    <select id="getPermissionByUserId" resultMap="BaseResultMap">
        select sm.*
        from sys_user_role sur
        left join sys_role_menu srm on srm.role_id = sur.role_id
        left join sys_menu      sm on sm.id = srm.menu_id
        <where>
            <if test="userId != null and userId != ''">
                sur.user_id = #{userId}
            </if>
            and sm.menu_type = 3
        </where>
        order by sm.rank
    </select>

    <select id="getMenuByRoleId" resultMap="BaseResultMap">
        select sm.*
        from sys_role_menu srm
        left join sys_menu      sm on sm.id = srm.menu_id
        <where>
            <if test="roleId != null and roleId != ''">
                srm.role_id = #{roleId}
            </if>
        </where>
    </select>

    <select id="getMenuByUserId" resultMap="BaseResultMap">
        select DISTINCT sm.*
        from sys_user_role sur
        left join sys_role_menu srm on srm.role_id = sur.role_id
        left join sys_menu      sm on sm.id = srm.menu_id
        <where>
            <if test="userId != null and userId != ''">
                sur.user_id = #{userId}
            </if>
            and sm.menu_type &lt; 3
        </where>
       order by sm.rank
    </select>
</mapper>