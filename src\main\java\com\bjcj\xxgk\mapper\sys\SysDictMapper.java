package com.bjcj.xxgk.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.xxgk.model.pojo.sys.SysDict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/29 16:15 周四
 */
public interface SysDictMapper extends BaseMapper<SysDict> {

    SysDict getDictByUserId(String userId);


    List<SysDict> getProjectClassifyByRoleId(@Param("roleId") String roleId);
    List<SysDict> getProjectClassifyByUserId(@Param("userId") String userId);

}