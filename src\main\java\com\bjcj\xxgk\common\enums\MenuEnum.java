package com.bjcj.xxgk.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 目录类型
 * <AUTHOR>
 * @date 2024/8/20 18:03 周二
 */
@Getter
@AllArgsConstructor
public enum MenuEnum {

    /**
     * 菜单
     */
    MENU(0),

    /**
     * frame
     */
    FRAME(1),

    /**
     * 外链
     */
    LINK(2),

    /**
     * 按钮
     */
    BUTTON(3);

    @EnumValue
    @JsonValue
    private final Integer code;
}
