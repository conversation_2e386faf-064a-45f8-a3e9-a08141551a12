package com.bjcj.xxgk.model.dto.sys;

import com.bjcj.xxgk.model.pojo.sys.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/21 10:14 周三
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SysUserDto extends SysUser {

    @NotNull(message = "roleIds是必填的")
    private List<String> roleIds;

    @Schema(description = "分管部门id集合")
    private List<String> branchDepartmentIds;

}
