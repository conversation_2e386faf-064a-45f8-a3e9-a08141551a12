<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysJobMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysJob">
        <!--@Table sys_job-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="amount" jdbcType="VARCHAR" property="amount"/>
    </resultMap>
    <resultMap id="JobWeightResultMap" type="com.bjcj.xxgk.model.dto.sys.SysJobDto">
        <!--@Table sys_job-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="amount" jdbcType="VARCHAR" property="amount"/>
        <collection property="jobWeightList" ofType="com.bjcj.xxgk.model.dto.sys.SysWeightDto">
            <id column="jobId" jdbcType="VARCHAR" property="jobId"/>
            <id column="sj1_name" jdbcType="VARCHAR" property="name"/>
            <result column="weight" jdbcType="VARCHAR" property="weight"/>
            <result column="receive_id" jdbcType="VARCHAR" property="receiveId"/>
            <result column="sj1_amount" jdbcType="VARCHAR" property="amount"/>
            <result column="sj1_sort" jdbcType="VARCHAR" property="sort"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        id, "name", code, create_time, update_time, "status", sort, "operator", amount
    </sql>

    <select id="getJobByUserId" resultMap="BaseResultMap">
        select sj.*
        from sys_job       sj
        left join sys_user su on su.job_id = sj.id
        where su.id = #{userId}
    </select>

    <select id="getJobWeight" resultMap="JobWeightResultMap">
        select
            sj.*, sjw.judge1 as jobId, sj1.name as sj1_name, sjw.weight, sjw.job_id as receive_id,
            sjw.amount as sj1_amount, sj1.sort as sj1_sort
        from sys_job sj
        left join sys_job_weight sjw on sjw.job_id = sj.id
        left join sys_job sj1 on sjw.judge1 = sj1.id
        where sj.id = #{jobId}
        order by sj1.sort
    </select>
</mapper>