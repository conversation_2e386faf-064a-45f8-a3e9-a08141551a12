package com.bjcj.xxgk.model.vo;

import com.bjcj.xxgk.model.pojo.sys.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 15:34
 */
@Schema(description = "用户展示")
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SysUserVo extends SysUser {

    @Schema(description = "部门")
    private SysDepartment sysDepartment;

    @Schema(description = "角色")
    private List<SysRole> sysRoles;

    @Schema(description = "岗位")
    private SysJob sysJob;

    @Schema(description = "项目")
    private SysDict project;

    @Schema(description = "分管部门")
    private List<SysDepartment> sysUserBranchDep;

    @Schema(description = "菜单权限id数组")
    private List<String> sysPermissionIds;


}
