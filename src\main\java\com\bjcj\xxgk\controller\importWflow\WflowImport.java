package com.bjcj.xxgk.controller.importWflow;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@SaCheckLogin
@RestController
@RequestMapping("/project")
@Tag(name = "项目导入")
public class WflowImport {
    @Operation(summary = "导入", description = "导入ZIP文件")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "file", description = "文件流", required = true, ref = "file")
    })
    @PostMapping("/import")
    public void importProject(@RequestParam("file") MultipartFile file){

    }
}
