package com.bjcj.xxgk.common.exception;

import com.bjcj.xxgk.common.domain.Code;
import com.bjcj.xxgk.common.domain.Message;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务异常
 * <AUTHOR>
 * @date 2021-08-27 14:56
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BusinessException extends RuntimeException {
    
    /**
     * 状态码
     */
    private final int code;
    
    /**
     * 自定义异常
     * @param message 错误信息
     */
    public BusinessException(String message) {
        super(message);
        this.code = Code.ERROR.value();
    }
    
    /**
     * 自定义异常
     * @param code    状态码
     * @param message 错误信息
     */
    public BusinessException(Code code, Message message) {
        super(message.message());
        this.code = code.value();
    }
    
    /**
     * @param code    状态码
     * @param message 错误信息
     */
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }
}

