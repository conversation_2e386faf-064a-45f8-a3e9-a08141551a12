package com.bjcj.xxgk.common.utils;

import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentParser;
import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/11/29 17:43
 */
public class UserAgentUtil {

    public static UserAgent userAgent(HttpServletRequest request) {
        return UserAgentParser.parse(request.getHeader("User-Agent"));
    }

    /**
     * 检测是否是移动设备访问
     *
     * @param request {@link HttpServletRequest}
     * @return true:移动设备接入，false:pc端接入
     */
    public static boolean isMobileOrPc(HttpServletRequest request) {
        return userAgent(request).isMobile();
    }

    /**
     * 是否为安卓设备
     *
     * @param request {@link HttpServletRequest}
     * @return 是否为安卓设备
     */
    public static boolean isAndroid(HttpServletRequest request) {
        return userAgent(request).getPlatform().isAndroid();
    }

    /**
     * 是否为IOS平台，包括IPhone、IPod、IPad
     *
     * @param request {@link HttpServletRequest}
     * @return 是否为苹果设备
     */
    public static boolean isIos(HttpServletRequest request) {
        return userAgent(request).getPlatform().isIos();
    }

    /**
     * 获取操作系统名称
     *
     * @return Android/Windows/iOS
     */
    public static String getOsName(HttpServletRequest request) {
        return userAgent(request).getOs().toString();
    }

    /**
     * 浏览器名称
     *
     * @return 浏览器名称
     */
    public static String getBrowserName(HttpServletRequest request) {
        return userAgent(request).getBrowser().toString();
    }

    /**
     * 获取浏览器版本
     *
     * @return Chrome Mobile 53.0.2785.146/Chrome 63.0.3239.84
     */
    public static String getBrowserVersion(HttpServletRequest request) {
        return userAgent(request).getVersion();
    }

    /**
     * 获取移动用户操作系统
     *
     * @return 用户操作系统
     */
    public static String getMobileOs(HttpServletRequest request) {
        UserAgent userAgent = userAgent(request);
        if (userAgent.isMobile() && userAgent.getPlatform().isMobile()) {
            if (userAgent.getPlatform().isAndroid()) {
                return "Android";
            } else if (userAgent.getPlatform().isIos()) {
                return "IPhone";
            } else if (userAgent.getPlatform().isIPad()) {
                return "IPad";
            } else {
                return "其他";
            }
        }

        return "其他";
    }

    /**
     * 获取移动用户操作系统
     *
     * @return 用户操作系统
     */
    public static String getDevice(HttpServletRequest request) {
        return getOsName(request) + "---" + getBrowserName(request) + "---" + getBrowserVersion(request);
    }


}
