package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024-09-23 17:33 周一
 */
@Schema
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_weather")
public class SysWeather {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    /**
     * 时间
     */
    @TableField(value = "\"day\"")
    @Schema(description = "时间")
    private LocalDate day;

    /**
     * 天气结果
     */
    @TableField(value = "json")
    @Schema(description = "天气结果")
    private String json;
}