package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/8/20 15:08 周二
 */
@Schema(description = "系统角色表")
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_role")
public class SysRole extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    @TableField(value = "\"name\"")
    @Schema(description = "名称")
    private String name;

    @TableField(value = "code")
    @Schema(description = "代码")
    private String code;

    @TableField(value = "sort")
    @Schema(description = "排序")
    private Short sort;

    @TableField(value = "\"status\"")
    @Schema(description = "状态 0未启用 1正常")
    private Short status;

    @TableField(value = "is_bear")
    @Schema(description = "是否项目承办人")
    private Boolean isBear = false;

    @TableField(value = "remark")
    @Schema(description = "备注")
    private String remark;
}