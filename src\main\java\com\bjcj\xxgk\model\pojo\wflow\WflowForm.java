package com.bjcj.xxgk.model.pojo.wflow;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value="wflow_form")
public class WflowForm {
    /**
    * 主键ID
    */
    @Schema(description="主键ID")
    private String id;

    /**
    * 实例ID
    */
    @Schema(description="实例ID")
    private String instanceId;

    /**
    * 表单名称
    */
    @Schema(description="表单名称")
    private String materialName;

    /**
    * 表单项
    */
    @Schema(description="表单项")
    private String formItems;

    /**
    * 表单配置
    */
    @Schema(description="表单配置")
    private String formConfig;

    /**
    * 目录id
    */
    @Schema(description="目录id")
    private String formDirId;
}