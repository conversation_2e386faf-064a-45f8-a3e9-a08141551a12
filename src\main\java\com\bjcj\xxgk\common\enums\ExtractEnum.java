package com.bjcj.xxgk.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 目录类型
 * <AUTHOR>
 * @date 2024/8/20 18:03 周二
 */
@Getter
@AllArgsConstructor
public enum ExtractEnum {

    /**
     * 成功
     */
    SUCCESS(0),

    /**
     * 失败
     */
    ERROR(1),

    /**
     * 警告
     */
    WARN(2),

    ;

    @EnumValue
    @JsonValue
    private final Integer code;
}
