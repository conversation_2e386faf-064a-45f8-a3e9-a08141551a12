package com.bjcj.xxgk.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:59 周四
 */
@Schema(description = "菜单meta 动画展示对象")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SysMenuMetaTransitionVo {

    @Schema(description = "进场动画")
    private String enterTransition;

    @Schema(description = "离场动画")
    private String leaveTransition;

}