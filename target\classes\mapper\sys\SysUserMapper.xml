<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysUserMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysUser">
        <!--@Table sys_user-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="telephone" jdbcType="VARCHAR" property="telephone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="age" jdbcType="VARCHAR" property="age"/>
        <result column="birthday" jdbcType="VARCHAR" property="birthday"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="is_rate" jdbcType="BOOLEAN" property="isRate"/>
        <result column="is_del" jdbcType="BOOLEAN" property="isDel"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="department_id" jdbcType="VARCHAR" property="departmentId"/>
        <result column="job_id" jdbcType="VARCHAR" property="jobId"/>
        <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
    </resultMap>
    <resultMap id="DepAndJobAndRoleResultMap" type="com.bjcj.xxgk.model.dto.sys.SysUserDepJobProjectDto">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="telephone" jdbcType="VARCHAR" property="telephone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="age" jdbcType="VARCHAR" property="age"/>
        <result column="birthday" jdbcType="VARCHAR" property="birthday"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="is_rate" jdbcType="BOOLEAN" property="isRate"/>
        <result column="is_del" jdbcType="BOOLEAN" property="isDel"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="department_id" jdbcType="VARCHAR" property="departmentId"/>
        <result column="job_id" jdbcType="VARCHAR" property="jobId"/>
        <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
        <association property="sysDepartment" javaType="com.bjcj.xxgk.model.pojo.sys.SysDepartment">
            <id column="sd_id" jdbcType="VARCHAR" property="id"/>
            <result column="sd_name" jdbcType="VARCHAR" property="name"/>
            <result column="sd_create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="sd_update_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="sd_status" jdbcType="SMALLINT" property="status"/>
            <result column="sd_parent_id" jdbcType="VARCHAR" property="parentId"/>
            <result column="sd_code" jdbcType="VARCHAR" property="code"/>
            <result column="sd_sort" jdbcType="SMALLINT" property="sort"/>
            <result column="sd_operator" jdbcType="VARCHAR" property="operator"/>
        </association>
        <association property="sysJob" javaType="com.bjcj.xxgk.model.pojo.sys.SysJob">
            <id column="sj_id" jdbcType="VARCHAR" property="id"/>
            <result column="sj_name" jdbcType="VARCHAR" property="name"/>
            <result column="sj_code" jdbcType="VARCHAR" property="code"/>
            <result column="sj_create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="sj_update_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="sj_status" jdbcType="SMALLINT" property="status"/>
            <result column="sj_sort" jdbcType="SMALLINT" property="sort"/>
            <result column="sj_operator" jdbcType="VARCHAR" property="operator"/>
            <result column="sj_amount" jdbcType="VARCHAR" property="amount"/>
        </association>
        <collection property="project" ofType="com.bjcj.xxgk.model.pojo.sys.SysDict">
            <id column="sdi_id" jdbcType="VARCHAR" property="id"/>
            <result column="sdi_sort" jdbcType="SMALLINT" property="sort"/>
            <result column="sdi_label" jdbcType="VARCHAR" property="label"/>
            <result column="sdi_value" jdbcType="VARCHAR" property="value"/>
            <result column="sdi_code" jdbcType="VARCHAR" property="code"/>
            <result column="sdi_is_default" jdbcType="BOOLEAN" property="isDefault"/>
            <result column="sdi_status" jdbcType="SMALLINT" property="status"/>
            <result column="sdi_create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="sdi_update_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="sdi_operator" jdbcType="VARCHAR" property="operator"/>
            <result column="sdi_parent_id" jdbcType="VARCHAR" property="parentId"/>
            <result column="sdi_group_name" jdbcType="VARCHAR" property="groupName"/>
            <result column="sdi_remark" jdbcType="VARCHAR" property="remark"/>
            <result column="sdi_color" jdbcType="VARCHAR" property="color"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id, real_name, username, password, status, create_time, update_time, operator, telephone, email, sex, age,
        birthday, sort, is_rate, is_del, id_number, department_id, job_id, project_id
    </sql>

    <select id="getUserDepJobProjectList" resultMap="DepAndJobAndRoleResultMap">
        select su.id, su.real_name, su.username, su.password, su.status, su.create_time, su.update_time, su.operator,
               su.telephone, su.email, su.sex, su.age, su.birthday, su.sort, su.is_rate, su.is_del, su.id_number,
               su.department_id, su.job_id, su.project_id,
               sd.id as sd_id, sd.name as sd_name, sd.create_time as sd_create_time,
               sd.update_time as sd_update_time, sd.status as sd_status,
               sd.parent_id as sd_parent_id, sd.code as sd_code, sd.sort as sd_sort, sd.operator as sd_operator,
               sj.id as sj_id, sj.name as sj_name, sj.code as sj_code, sj.create_time as sj_create_time,
               sj.update_time as sj_update_time, sj.status as sj_status, sj.sort as sj_sort, sj.operator as sj_operator,
               sj.amount as sj_amount,
               sdi.id as sdi_id, sdi.sort as sdi_sort, sdi."label" as sdi_label, sdi."value" as sdi_value,
               sdi.code as sdi_code, sdi.is_default as sdi_is_default, sdi."status" as sdi_status,
               sdi.create_time as sdi_create_time, sdi.update_time as sdi_update_time, sdi."operator" as sdi_operator,
               sdi.parent_id as sdi_parent_id, sdi.group_name as sdi_group_name, sdi.remark as sdi_remark,
               sdi.color as sdi_color

        from sys_user            su
        left join sys_department sd on sd.id = su.department_id
        left join sys_job        sj on sj.id = su.job_id
        left join sys_dict       sdi on sdi.id = su.project_id
        <where>
            <if test="sysUserQo.departmentId != null and sysUserQo.departmentId != ''">
                su.department_id = #{sysUserQo.departmentId}
            </if>
            <if test="sysUserQo.jobId != null and sysUserQo.jobId != ''">
                and su.job_id = #{sysUserQo.jobId}
            </if>
            <if test="sysUserQo.status != null">
                and su.status = #{sysUserQo.status}
            </if>
            <if test="sysUserQo.username != null and sysUserQo.username != ''">
                and (su.username like concat('%', #{sysUserQo.username}, '%')
                    or su.real_name like concat('%', #{sysUserQo.username}, '%'))
            </if>
            <if test="sysUserQo.isRate != null">
                and su.is_rate = #{sysUserQo.isRate}
            </if>
            <if test="sysUserQo.isShowOperator != null and sysUserQo.isShowOperator == false">
                and sj.code != 'SYS'
            </if>
            and su.is_del = false
            and su.username != 'admin'
        </where>
        order by su.sort, su.create_time desc
    </select>

    <select id="getUserDepJobProjectListNotPage" resultMap="DepAndJobAndRoleResultMap">
        select su.id, su.real_name, su.username, su.password, su.status, su.create_time, su.update_time, su.operator,
               su.telephone, su.email, su.sex, su.age, su.birthday, su.sort, su.is_rate, su.is_del, su.id_number,
               su.department_id, su.job_id, su.project_id,
               sd.id as sd_id, sd.name as sd_name, sd.create_time as sd_create_time,
               sd.update_time as sd_update_time, sd.status as sd_status,
               sd.parent_id as sd_parent_id, sd.code as sd_code, sd.sort as sd_sort, sd.operator as sd_operator,
               sj.id as sj_id, sj.name as sj_name, sj.code as sj_code, sj.create_time as sj_create_time,
               sj.update_time as sj_update_time, sj.status as sj_status, sj.sort as sj_sort, sj.operator as sj_operator,
               sj.amount as sj_amount,
               sdi.id as sdi_id, sdi.sort as sdi_sort, sdi."label" as sdi_label, sdi."value" as sdi_value,
               sdi.code as sdi_code, sdi.is_default as sdi_is_default, sdi."status" as sdi_status,
               sdi.create_time as sdi_create_time, sdi.update_time as sdi_update_time, sdi."operator" as sdi_operator,
               sdi.parent_id as sdi_parent_id, sdi.group_name as sdi_group_name, sdi.remark as sdi_remark

        from sys_user            su
        left join sys_department sd on sd.id = su.department_id
        left join sys_job        sj on sj.id = su.job_id
        left join sys_dict       sdi on sdi.id = su.project_id
        <where>
            <if test="sysUserQo.departmentId != null and sysUserQo.departmentId != ''">
                su.department_id = #{sysUserQo.departmentId}
            </if>
            <if test="sysUserQo.jobId != null and sysUserQo.jobId != ''">
                and su.job_id = #{sysUserQo.jobId}
            </if>
            <if test="sysUserQo.status != null">
                and su.status = #{sysUserQo.status}
            </if>
            <if test="sysUserQo.realName != null and sysUserQo.realName != ''">
                and su.real_name like concat('%', #{sysUserQo.realName}, '%')
            </if>
            <if test="sysUserQo.isRate != null">
                and su.is_rate = #{sysUserQo.isRate}
            </if>
            and su.is_del = false
        </where>
        order by su.sort asc
    </select>

    <select id="getUserByRoleId" resultMap="DepAndJobAndRoleResultMap">
        select su.id, su.real_name, su.username, su.password, su.status, su.create_time, su.update_time, su.operator,
               su.telephone, su.email, su.sex, su.age, su.birthday, su.sort, su.is_rate, su.is_del, su.id_number,
               sd.id as sd_id, sd.name as sd_name, sd.create_time as sd_create_time,
               sd.update_time as sd_update_time, sd.status as sd_status,
               sd.parent_id as sd_parent_id, sd.code as sd_code, sd.sort as sd_sort, sd.operator as sd_operator,
               sj.id as sj_id, sj.name as sj_name, sj.code as sj_code, sj.create_time as sj_create_time,
               sj.update_time as sj_update_time, sj.status as sj_status, sj.sort as sj_sort, sj.operator as sj_operator,
               sj.amount as sj_amount

        from sys_user            su
        left join sys_department sd on sd.id = su.department_id
        left join sys_job        sj on sj.id = su.job_id
        left join sys_user_role  sur on su.id = sur.user_id
        <where>
            <if test="roleId != null and roleId != ''">
                and sur.role_id = #{roleId}
            </if>
            <if test="isShowOperator != null and isShowOperator == false">
                and sj.code != 'SYS'
            </if>
            and su.is_del = false
        </where>
        order by su.sort, su.create_time
    </select>

    <update id="updateRateBatch">
        update sys_user
        set is_rate = #{isRate} where id in
        <foreach collection="idss" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectRateCount" resultType="int">
        select count(*) as rate_count
        from sys_user
        where is_rate = true and
              is_del = false
    </select>

    <select id="getUserDepJobProjectById" resultMap="DepAndJobAndRoleResultMap">
        select su.id, su.real_name, su.username, su.password, su.status, su.create_time, su.update_time, su.operator,
               su.telephone, su.email, su.sex, su.age, su.birthday, su.sort, su.is_rate, su.is_del, su.id_number,
               su.department_id, su.job_id, su.project_id,
               sd.id as sd_id, sd.name as sd_name, sd.create_time as sd_create_time,
               sd.update_time as sd_update_time, sd.status as sd_status,
               sd.parent_id as sd_parent_id, sd.code as sd_code, sd.sort as sd_sort, sd.operator as sd_operator,
               sj.id as sj_id, sj.name as sj_name, sj.code as sj_code, sj.create_time as sj_create_time,
               sj.update_time as sj_update_time, sj.status as sj_status, sj.sort as sj_sort, sj.operator as sj_operator,
               sj.amount as sj_amount,
               sdi.id as sdi_id, sdi.sort as sdi_sort, sdi."label" as sdi_label, sdi."value" as sdi_value,
               sdi.code as sdi_code, sdi.is_default as sdi_is_default, sdi."status" as sdi_status,
               sdi.create_time as sdi_create_time, sdi.update_time as sdi_update_time, sdi."operator" as sdi_operator,
               sdi.parent_id as sdi_parent_id, sdi.group_name as sdi_group_name, sdi.remark as sdi_remark

        from sys_user            su
        left join sys_department sd on sd.id = su.department_id
        left join sys_job        sj on sj.id = su.job_id
        left join sys_dict       sdi on sdi.id = su.project_id
        where su.is_del = false and su.id = #{userId}
        order by su.sort, su.create_time desc
    </select>

    <select id="allReportOfUser" resultType="com.bjcj.xxgk.model.pojo.sys.SysUser">
        SELECT su.*, cur.report_file_url as reportUrl,
               (cur.report_file_url IS NOT NULL) AS reportSubmitStatus, cur.create_time as reportUpdateTime
        FROM sys_user               su
        left join sys_job           sj on sj.id = su.job_id
        left join cycle_user_report cur on su.id = cur.user_id and cur.cycle_id = #{cycleId}
        <where>
            <if test="realName != null and realName != ''">
                su.real_name like concat('%', #{realName}, '%')
            </if>
            <if test="reportSubmitStatus != null">
                and reportSubmitStatus = #{reportSubmitStatus}
            </if>
            and sj.name != '系统操作人员'
            and su.is_del = false
            and su.is_rate = true
        </where>
        order by su.sort
    </select>

    <select id="reportOfBkhUser" resultMap="BaseResultMap">
        SELECT su.*, cur.report_file_url as reportUrl,
               (cur.report_file_url IS NOT NULL) AS reportSubmitStatus, cur.create_time as reportUpdateTime
        FROM sys_user               su
        left join sys_job           sj on sj.id = su.job_id
        left join cycle_user_report cur on su.id = cur.user_id and cur.cycle_id = #{cycleId}
        <where>
            <if test="realName != null and realName != ''">
                su.real_name like concat('%', #{realName}, '%')
            </if>
            <if test="reportSubmitStatus != null">
                and reportSubmitStatus = #{reportSubmitStatus}
            </if>
            <if test="userIds != null and userIds.size() != 0">
                and su.id in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            and sj.name != '系统操作人员'
            and su.is_del = false
            and su.is_rate = true
        </where>
        order by su.sort
    </select>

    <select id="selectDeptRateUserCount" resultType="java.lang.Integer">
        select count(*)
        from sys_user
        where department_id = #{id}
          and is_rate = true
          and is_del = false
    </select>

    <select id="selectUserDetailsInfoByJobId" resultType="com.bjcj.xxgk.model.qo.SysUserDeptJobQo">
        select su.*, sj.id as job_id, sj.name as job_name, sd.id as dept_id, sd.name as dept_name
        from sys_user            as su
        left join sys_job        as sj on sj.id = su.job_id
        left join sys_department as sd on sd.id = su.department_id
        where su.job_id = #{jobId}
          and su.is_del = false
          and su.is_rate = true
    </select>

    <select id="selectUserDetailsInfoByDeptIdAndJobId" resultType="com.bjcj.xxgk.model.qo.SysUserDeptJobQo">
        select su.*, sj.id as job_id, sj.name as job_name, sd.id as dept_id, sd.name as dept_name
        from sys_user            as su
        left join sys_job        as sj on sj.id = su.job_id
        left join sys_department as sd on sd.id = su.department_id
        where su.job_id = #{jobId}
          and su.department_id = #{deptId}
          and su.is_del = false
          and su.is_rate = true
    </select>

    <select id="selectAllYearBkhUserInfo" resultType="com.bjcj.xxgk.model.qo.BkhUserQo">
        select su.id as userid, su.real_name as real_name,
               sd.name as dept_name, sd.code as dept_code,
               sj.id as job_id, sj.name as job_name
        from sys_user            as su
        left join sys_department as sd on su.department_id = sd.id
        left join sys_job        as sj on su.job_id = sj.id
        where su.is_del = false
          and su.is_rate = true
          and su.id != #{userId}
        order by su.sort
    </select>

    <select id="selectAllBkhUserInfo" resultMap="DepAndJobAndRoleResultMap">
        select su.id, su.real_name, su.username, su.password, su.status, su.create_time, su.update_time, su.operator,
               su.telephone, su.email, su.sex, su.age, su.birthday, su.sort, su.is_rate, su.is_del, su.id_number,
               su.department_id, su.job_id, su.project_id,
               sd.id as sd_id, sd.name as sd_name, sd.create_time as sd_create_time,
               sd.update_time as sd_update_time, sd.status as sd_status,
               sd.parent_id as sd_parent_id, sd.code as sd_code, sd.sort as sd_sort, sd.operator as sd_operator,
               sj.id as sj_id, sj.name as sj_name, sj.code as sj_code, sj.create_time as sj_create_time,
               sj.update_time as sj_update_time, sj.status as sj_status, sj.sort as sj_sort, sj.operator as sj_operator,
               sj.amount as sj_amount
        from sys_user            as su
        left join sys_department as sd on su.department_id = sd.id
        left join sys_job        as sj on su.job_id = sj.id
        where su.is_del = false and su.is_rate = true and su.id != #{userId}
        order by su.sort
    </select>

</mapper>