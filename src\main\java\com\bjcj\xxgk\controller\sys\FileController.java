package com.bjcj.xxgk.controller.sys;

import com.bjcj.xxgk.common.domain.FileInfo;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.enums.FileTypeEnum;
import com.bjcj.xxgk.common.utils.FileUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/6 16:01
 */
@Tag(name = "00文件管理")
@RestController
@RequestMapping("/file")
public class FileController {

    @Operation(summary = "上传文件", description = "上传文件")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "file", description = "文件流", required = true,ref = "file"),
            @Parameter(name = "fileType", description = "文件类型", required = true),
    })
    @PostMapping("/upload")
    public JsonResult uploadFile(@RequestParam("file") MultipartFile file, FileTypeEnum fileType) {
        FileInfo fileInfo = FileUtil.uploadFile(file, fileType);
        return JsonResult.success(fileInfo);
    }

    @Operation(summary = "多文件上传", description = "多文件上传")
    @ApiOperationSupport(order = 2)
    @Parameters({
            @Parameter(name = "files", description = "文件流", required = true, ref = "file"),
            @Parameter(name = "fileType", description = "文件类型", required = true)
    })
    @PostMapping("/uploads")
    public JsonResult uploadFiles(@RequestParam("files") MultipartFile[] files, FileTypeEnum fileType) {
        List<FileInfo> fileInfo = FileUtil.uploadFiles(files, fileType);
        return JsonResult.success(fileInfo);
    }

    @Operation(summary = "下载文件", description = "下载文件")
    @ApiOperationSupport(order = 3)
    @Parameter(name = "url", description = "文件路径", required = true)
    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadFile(@RequestParam("url") String url) throws Exception {
        return FileUtil.downloadFile(url);
    }

}
