package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-09-20 10:46 周五
 */
@Schema(description = "用户表")
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_user")
public class SysUser extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    /**
     * 实名
     */
    @TableField(value = "real_name")
    @Schema(description = "实名")
    private String realName;

    /**
     * 用户名
     */
    @TableField(value = "username")
    @Schema(description = "用户名")
    private String username;

    /**
     * 密码
     */
    @TableField(value = "\"password\"")
    @Schema(description = "密码")
    private String password;

    /**
     * 状态0未启用1正常
     */
    @TableField(value = "\"status\"")
    @Schema(description = "状态0未启用1正常")
    private Short status;

    /**
     * 手机号
     */
    @TableField(value = "telephone")
    @Schema(description = "手机号")
    private String telephone;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    @Schema(description = "邮箱")
    private String email;

    /**
     * 性别
     */
    @TableField(value = "sex")
    @Schema(description = "性别")
    private String sex;

    /**
     * 年龄
     */
    @TableField(value = "age")
    @Schema(description = "年龄")
    private String age;

    /**
     * 生日
     */
    @TableField(value = "birthday")
    @Schema(description = "生日")
    private String birthday;

    /**
     * 排序
     */
    @TableField(value = "sort")
    @Schema(description = "排序")
    private Short sort;

    /**
     * 是否打分
     */
    @TableField(value = "is_rate")
    @Schema(description = "是否打分")
    private Boolean isRate;

    /**
     * 逻辑删除
     */
    @TableField(value = "is_del")
    @TableLogic
    @Schema(description = "逻辑删除")
    private Boolean isDel = false;

    /**
     * 身份证号
     */
    @TableField(value = "id_number")
    @Schema(description = "身份证号")
    private String idNumber;

    /**
     * 部门id
     */
    @TableField(value = "department_id")
    @Schema(description = "部门id")
    private String departmentId;

    /**
     * 职位id
     */
    @TableField(value = "job_id")
    @Schema(description = "职位id")
    private String jobId;

    /**
     * 项目字典类型id
     */
    @TableField(value = "project_id")
    @Schema(description = "项目字典类型id")
    private String projectId;

    @TableField(exist = false)
    @Schema(description = "报告提交状态")
    private Boolean reportSubmitStatus;

    @TableField(exist = false)
    @Schema(description = "报告Url")
    private String reportUrl;

    @TableField(exist = false)
    @Schema(description = "报告上传时间")
    private LocalDateTime reportUpdateTime;


}