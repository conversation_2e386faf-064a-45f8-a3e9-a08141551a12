package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.common.constant.OperaLogConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.pojo.sys.SysDepartment;
import com.bjcj.xxgk.serviceImpl.sys.SysDepartmentService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/8/27 8:12
 */
@SaCheckLogin
@RestController
@RequestMapping("/department")
@Tag(name = "05部门")
@Validated
public class DepartmentController {
    @Resource
    private SysDepartmentService sysDepartmentService;

    @Operation(summary = "管理中心部门树结构", description = "管理中心部门树结构")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult departmentTree() {
        return sysDepartmentService.manageTree();
    }

    @Operation(summary = "展示页面部门树结构", description = "展示页面部门树结构")
    @ApiOperationSupport(order = 1)
    @GetMapping("/show/tree")
    public JsonResult showTree() {
        return sysDepartmentService.showTree();
    }

    @Operation(summary = "部门", description = "部门")
    @ApiOperationSupport(order = 2)
    @GetMapping("/{id}")
    public JsonResult department(@PathVariable("id") String id) {
        return JsonResult.success(sysDepartmentService.getById(id));
    }

    @SaCheckPermission("system:dept:add")
    @PostMapping
    @Operation(summary = "新增部门", description = "新增部门")
    @ApiOperationSupport(order = 3)
    @OperaLog(operaModule = "新增部门",operaType = OperaLogConstant.CREATE,operaDesc = "新增部门")
    public JsonResult add(@Validated @RequestBody SysDepartment sysdepartment) {
        return sysDepartmentService.add(sysdepartment);
    }

    @SaCheckPermission("system:dept:edit")
    @PutMapping
    @Operation(summary = "编辑部门", description = "编辑部门")
    @ApiOperationSupport(order = 4)
    @OperaLog(operaModule = "编辑部门",operaType = OperaLogConstant.UPDATE)
    public JsonResult fix(@Validated @RequestBody SysDepartment sysdepartment) {
        return sysDepartmentService.fix(sysdepartment);
    }

    @SaCheckPermission("system:dept:delete")
    @DeleteMapping("/{id}")
    @Operation(summary = "删除部门", description = "删除部门")
    @ApiOperationSupport(order = 5)
    @OperaLog(operaModule = "删除部门",operaType = OperaLogConstant.DELETE)
    public JsonResult delete(@PathVariable("id") String id) {
        return sysDepartmentService.del(id);
    }

}
