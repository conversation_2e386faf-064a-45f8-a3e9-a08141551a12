package com.bjcj.xxgk.model.dto.sys;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/26 18:19 周一
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SysWeightDto {

    @NotBlank(message = "jobId是必填的")
    private String jobId;

    private String receiveId;

    @NotBlank(message = "weight是必填的")
    private String weight;

    @NotBlank(message = "amount是必填的")
    private String amount;

    private String name;

    private Short sort;

}
