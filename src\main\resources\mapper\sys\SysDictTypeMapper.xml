<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysDictTypeMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysDictType">
    <!--@Table sys_dict_type-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="type" jdbcType="SMALLINT" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    id, "name", code, "status", "operator", create_time, update_time, remark, "type"
  </sql>
</mapper>