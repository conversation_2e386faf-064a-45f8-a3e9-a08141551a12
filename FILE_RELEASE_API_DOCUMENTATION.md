# 文件发布管理API文档

## 概述

本文档描述了文件/消息发布管理功能的REST API接口。该功能基于PostgreSQL数据库表`file_release`，提供完整的CRUD操作。

## 数据库表结构

```sql
CREATE TABLE "public"."file_release" (
  "id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "instance_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "news_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "file_header" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "release_time" timestamp(6),
  "release_user" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "status" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "details" text COLLATE "pg_catalog"."default"
);
```

## API端点

### 1. 创建新发布内容

**POST** `/api/file-release`

创建新的文件/消息发布内容。

**请求体示例：**
```json
{
  "instanceId": "INST_001",
  "newsType": "公告",
  "fileHeader": "重要通知：系统维护公告",
  "releaseTime": "2024-12-25T10:00:00",
  "releaseUser": "管理员",
  "status": "已发布",
  "details": "系统将于2024年12月26日进行维护，预计维护时间为2小时。"
}
```

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 2. 分页查询发布内容

**GET** `/api/file-release`

分页查询发布内容，支持多种过滤条件。

**查询参数：**
- `current` (必填): 当前页，默认为1
- `size` (必填): 每页数量，默认为10
- `instanceId` (可选): 实例ID
- `newsType` (可选): 新闻/消息类型
- `fileHeader` (可选): 文件/消息标题（支持模糊查询）
- `releaseUser` (可选): 发布者/作者（支持模糊查询）
- `status` (可选): 发布状态
- `releaseStartTime` (可选): 发布开始时间
- `releaseEndTime` (可选): 发布结束时间
- `createStartTime` (可选): 创建开始时间
- `createEndTime` (可选): 创建结束时间

**请求示例：**
```
GET /api/file-release?current=1&size=10&newsType=公告&status=已发布
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": "1234567890123456789",
        "instanceId": "INST_001",
        "newsType": "公告",
        "fileHeader": "重要通知：系统维护公告",
        "releaseTime": "2024-12-25T10:00:00",
        "releaseUser": "管理员",
        "status": "已发布",
        "createTime": "2024-12-25T09:00:00",
        "updateTime": "2024-12-25T09:00:00",
        "details": "系统将于2024年12月26日进行维护，预计维护时间为2小时。"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 3. 根据ID获取发布内容

**GET** `/api/file-release/{id}`

根据ID获取单个发布内容的详细信息。

**路径参数：**
- `id`: 发布内容的ID

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": "1234567890123456789",
    "instanceId": "INST_001",
    "newsType": "公告",
    "fileHeader": "重要通知：系统维护公告",
    "releaseTime": "2024-12-25T10:00:00",
    "releaseUser": "管理员",
    "status": "已发布",
    "createTime": "2024-12-25T09:00:00",
    "updateTime": "2024-12-25T09:00:00",
    "details": "系统将于2024年12月26日进行维护，预计维护时间为2小时。"
  }
}
```

### 4. 更新发布内容

**PUT** `/api/file-release/{id}`

更新现有的发布内容。

**路径参数：**
- `id`: 发布内容的ID

**请求体示例：**
```json
{
  "fileHeader": "更新后的标题",
  "status": "已撤回",
  "details": "更新后的详细内容"
}
```

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 5. 删除发布内容

**DELETE** `/api/file-release/{id}`

根据ID删除发布内容。

**路径参数：**
- `id`: 发布内容的ID

**响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

## 错误响应

当操作失败时，API会返回相应的错误信息：

```json
{
  "code": 500,
  "msg": "操作失败",
  "data": null
}
```

常见错误情况：
- 400: 请求参数验证失败
- 404: 资源不存在
- 500: 服务器内部错误

## 数据验证规则

### 创建请求验证
- `instanceId`: 必填，最大长度255字符
- `newsType`: 必填，最大长度255字符
- `fileHeader`: 必填，最大长度255字符
- `releaseUser`: 必填，最大长度255字符
- `status`: 必填，最大长度255字符
- `releaseTime`: 可选，如果不提供则默认为当前时间
- `details`: 可选，文本内容

### 更新请求验证
- 所有字段都是可选的
- 字符串字段最大长度255字符（除details外）
- 不能更新ID、创建时间等系统字段

### 分页查询验证
- `current`: 必填，必须大于0
- `size`: 必填，必须大于0
- 时间字段需要符合ISO 8601格式

## 注意事项

1. 所有API都需要用户登录认证（@SaCheckLogin）
2. 创建和更新操作会自动记录操作日志
3. 时间字段使用GMT+8时区
4. ID使用雪花算法自动生成
5. 创建时间和更新时间由系统自动维护
6. 分页查询结果按发布时间和创建时间倒序排列
