package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.common.constant.OperaLogConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.pojo.sys.SysDict;
import com.bjcj.xxgk.model.pojo.sys.SysDictType;
import com.bjcj.xxgk.serviceImpl.sys.SysDictService;
import com.bjcj.xxgk.serviceImpl.sys.SysDictTypeService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/8/16 9:44
 */
@SaCheckLogin
@RestController
@RequestMapping("/dict")
@Tag(name = "07字典")
@Validated
public class DictController {

    @Resource
    SysDictService sysDictService;

    @Resource
    SysDictTypeService sysDictTypeService;

    @Operation(summary = "字典类型的列表", description = "字典类型的列表")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult page(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize,
                           String dictName, String dictCode, Short status, String dictType,
                           String startTime, String endTime) {
        return sysDictTypeService.pageList(
                new Page<>(pageNum, pageSize), dictName, dictCode, status, dictType, startTime, endTime
        );
    }

    @SaCheckPermission(value = {"basics:dictionaries:add", "basics:dictionaries:edit"}, mode = SaMode.OR)
    @PostMapping
    @Operation(summary = "新增/编辑字典类型", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 2)
    @Transactional(rollbackFor = Exception.class)
    @OperaLog(operaModule = "新增/编辑字典类型",operaType = OperaLogConstant.CREATE_OR_UPDATE)
    public JsonResult addOrEdit(@RequestBody SysDictType sysDictType) {
        return JsonResult.success(sysDictTypeService.saveOrUpdate(sysDictType));
    }

    @SaCheckPermission("basics:dictionaries:delete")
    @DeleteMapping("/{id}")
    @Operation(summary = "删除字典", description = "删除字典")
    @ApiOperationSupport(order = 3)
    @Transactional(rollbackFor = Exception.class)
    @OperaLog(operaModule = "删除字典",operaType = OperaLogConstant.DELETE)
    public JsonResult delete(@PathVariable("id") String id) {
        return JsonResult.success(sysDictTypeService.removeById(id));
    }

    //=======================================================字典值=======================================================

    @SaCheckPermission(value = {"basics:dictionaries:val:add", "basics:dictionaries:val:edit"}, mode = SaMode.OR)
    @PostMapping("/data")
    @Operation(summary = "新增/编辑字典值", description = "新增不传id,编辑传id")
    @ApiOperationSupport(order = 4)
    @Transactional(rollbackFor = Exception.class)
    @OperaLog(operaModule = "新增/编辑字典值",operaType = OperaLogConstant.CREATE_OR_UPDATE)
    public JsonResult addOrEditDictValue(@RequestBody SysDict sysDict) {
        return sysDictService.addOrFix(sysDict);
    }

    @SaCheckPermission("basics:dictionaries:val:delete")
    @DeleteMapping("/data/{id}")
    @Operation(summary = "删除字典值", description = "删除字典值")
    @ApiOperationSupport(order = 5)
    @Transactional(rollbackFor = Exception.class)
    @OperaLog(operaModule = "删除字典值",operaType = OperaLogConstant.DELETE)
    public JsonResult deleteDictValue(@PathVariable("id") String id) {
        return sysDictService.del(id);
    }

    @SaCheckPermission("basics:dictionaries:data")
    @GetMapping("/data")
    @Operation(summary = "字典值-列表", description = "字典值-列表")
    @ApiOperationSupport(order = 6)
    public JsonResult dictDataList(@RequestParam String dictCode, String dictLabel, Short status, String groupName) {
        return sysDictService.dictDataList(dictCode, dictLabel, status, groupName);
    }

    // @GetMapping("/dictDataListManage")
    // @Operation(summary = "字典值-列表(管理,统一树结构)", description = "字典值-列表(管理,统一树结构)")
    // @ApiOperationSupport(order = 7)
    // @Parameters({
    //         @Parameter(name = "dictType", description = "字典类型", required = true),
    //         @Parameter(name = "dictLabel", description = "字典标签", required = false),
    //         @Parameter(name = "status", description = "状态（0正常 1停用）", required = false),
    //         @Parameter(name = "groupName", description = "组名", required = false)
    // })
    // public JsonResult dictDataListManage(@RequestParam("dictType") String dictType,
    //                                      String dictLabel, String status, String groupName) {
    //     return sysDictDataService.queryTreeList(dictType, dictLabel, status, groupName);
    // }

    @GetMapping("/{code}")
    @Operation(summary = "获取字典值", description = "获取字典值")
    @ApiOperationSupport(order = 8)
    @SaIgnore
    public JsonResult getDictByCode(@PathVariable String code) {
        return sysDictService.getDictByCode(code);
    }

    @GetMapping("/id/{id}")
    @Operation(summary = "获取字典值_通过id", description = "获取字典值_通过id")
    @ApiOperationSupport(order = 8)
    public JsonResult getDictById(@PathVariable String id) {
        return sysDictService.getDictById(id);
    }


}
