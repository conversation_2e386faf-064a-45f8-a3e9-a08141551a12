package com.bjcj.xxgk.common.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "xxgk")
@Configuration
@Data
public class XxgkProperties {

    /**
     * 地址
     */
    private String area;

    /**
     * 文件
     */
    private file file;

    /**
     * 是否校验验证码
     */
    private boolean checkCaptcha = false;

    /**
     * 默认密码
     */
    private String defaultPassword;

    /**
     * 聚合接口
     */
    private Jvhe jvhe;

    @Data
    public static class file {
        /**
         * 文件上传路径
         */
        private String upload;
        /**
         * 日志存储路径
         */
        private String log;
    }

    @Data
    public static class Jvhe {
        /**
         * 文件上传路径
         */
        private JvheWeather weather;
        /**
         * 日志存储路径
         */
        private JvheMessage message;
    }

    @Data
    public static class JvheApi {
        /**
         * 秘钥
         */
        private String ak;

        /**
         * 地址
         */
        private String url;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class JvheWeather extends JvheApi {
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class JvheMessage extends JvheApi {

        /**
         * 模板id
         */
        private List<Integer> tplIds;
        
        /** 
         * 内置跳转url
         */
        private String transUrl;
    }
}
