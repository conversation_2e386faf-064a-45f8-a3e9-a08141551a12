package com.bjcj.xxgk.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bjcj.xxgk.model.pojo.sys.SysDepartment;
import com.bjcj.xxgk.model.pojo.sys.SysUserBranchDep;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-29 12:01 周日
 */
public interface SysUserBranchDepMapper extends BaseMapper<SysUserBranchDep> {

    /**
     * <h2>用户分管部门</h2>
     * @param userId:
     * @return com.bjcj.kpi.model.pojo.sys.SysDepartment
     * <AUTHOR>
     * @date 2024-09-29 14:54
     */
    List<SysDepartment> getUserBranchDep(String userId);
}