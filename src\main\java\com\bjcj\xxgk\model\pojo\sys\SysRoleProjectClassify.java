package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-03-04 14:25 周二
 */
@Schema
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_role_project_classify")
public class SysRoleProjectClassify {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description="")
    private String id;

    /**
     * 角色id
     */
    @TableField(value = "role_id")
    @Schema(description="角色id")
    private String roleId;

    /**
     * 项目类型id
     */
    @TableField(value = "project_classify_id")
    @Schema(description="项目类型id")
    private String projectClassifyId;
}