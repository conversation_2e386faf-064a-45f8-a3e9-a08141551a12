<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysDepartmentMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysDepartment">
        <!--@Table sys_department-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, "name", create_time, update_time, "status", parent_id, code, sort, "operator"
    </sql>

    <select id="getDepartmentByUserId" resultMap="BaseResultMap">
        select sd.* from sys_user su
        left join sys_department sd on su.department_id = sd.id
        where su.id = #{userId}
    </select>
</mapper>