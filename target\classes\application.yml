server:
    port: 8019
    servlet:
        context-path: /xxgk

spring:
    application:
        name: performance-appraisal
    profiles:
        active: local
    main:
        allow-circular-references: true
    web:
        resources:
            static-locations: classpath:/static/


# springdoc-openapi项目配置
springdoc:
    api-docs:
        enabled: true
    swagger-ui:
        operationsSorter: method
        enabled: true
        tagsSorter: alpha
    group-configs:
        -   group: 'default'
            paths-to-match: '/**'
            packages-to-scan: com.bjcj.xxgk.controller
knife4j:
    enable: true
    setting:
        language: zh_cn
        swagger-model-name: 实体类列表
        enable-open-api: false
        enable-version: true