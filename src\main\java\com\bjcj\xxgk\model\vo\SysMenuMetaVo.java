package com.bjcj.xxgk.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/8/29 11:59 周四
 */
@Schema(description = "菜单meta展示对象")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class SysMenuMetaVo {

    @Schema(description = "菜单名称")
    private String title;

    @Schema(description = "菜单排序")
    private Short rank;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "菜单右侧图标")
    private String extraIcon;

    @Schema(description = "菜单激活")
    private String activePath;

    @Schema(description = "类型为按钮时的权限标识")
    private String auths;

    @Schema(description = "iframe 链接地址")
    private String frameSrc;

    @Schema(description = "是否显示iframe加载动画")
    private Boolean frameLoading;

    @Schema(description = "是否显示菜单")
    private Boolean showLink;

    @Schema(description = "是否显示父级菜单")
    private Boolean showParent;

    @Schema(description = "是否缓存页面")
    private Boolean keepAlive;

    @Schema(description = "是否允许加载到标签页")
    private Boolean hiddenTag;

    @Schema(description = "是否固定标签页")
    private Boolean fixedTag;

    @Schema(description = "菜单 动画 元数据 对象")
    private SysMenuMetaTransitionVo transition;


}