package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.common.constant.OperaLogConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.pojo.sys.SysMenu;
import com.bjcj.xxgk.serviceImpl.sys.SysMenuService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/8/20 18:36
 */
@SaCheckLogin
@RestController
@RequestMapping("/menu")
@Tag(name = "06菜单")
@Validated
public class MenuController {
    @Resource
    private SysMenuService menuService;

    @Operation(summary = "菜单管理-树结构", description = "菜单管理-树结构")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult menuPage() {
        return menuService.tree();
    }

    @Operation(summary = "菜单", description = "菜单")
    @ApiOperationSupport(order = 2)
    @GetMapping("/{id}")
    public JsonResult menu(@PathVariable("id") String id) {
        return JsonResult.success(menuService.getById(id));
    }

    @SaCheckPermission("system:menu:add")
    @PostMapping
    @Operation(summary = "新增菜单", description = "新增菜单")
    @ApiOperationSupport(order = 3)
    @OperaLog(operaModule = "新增菜单",operaType = OperaLogConstant.CREATE)
    public JsonResult add(@RequestBody SysMenu sysmenu) {
        return menuService.add(sysmenu);
    }

    @SaCheckPermission("system:menu:edit")
    @PutMapping
    @Operation(summary = "编辑菜单", description = "编辑菜单")
    @ApiOperationSupport(order = 4)
    @OperaLog(operaModule = "编辑菜单",operaType = OperaLogConstant.UPDATE)
    public JsonResult fix(@RequestBody SysMenu sysmenu) {
        return menuService.fix(sysmenu);
    }

    @SaCheckPermission("system:menu:delete")
    @DeleteMapping("/{id}")
    @Operation(summary = "删除菜单", description = "删除菜单")
    @ApiOperationSupport(order = 5)
    @OperaLog(operaModule = "删除菜单",operaType = OperaLogConstant.DELETE)
    public JsonResult delete(@PathVariable("id") String id) {
        return menuService.del(id);
    }

}
