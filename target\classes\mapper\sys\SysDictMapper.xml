<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysDictMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysDict">
        <!--@Table sys_dict-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="sort" jdbcType="SMALLINT" property="sort"/>
        <result column="label" jdbcType="VARCHAR" property="label"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="is_default" jdbcType="BOOLEAN" property="isDefault"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, sort, "label", "value", code, is_default, "status", create_time, update_time,
        "operator", parent_id, group_name, remark, color
    </sql>

    <select id="getDictByUserId" resultMap="BaseResultMap">
        select sd.*
        from sys_dict sd
        left join sys_user su on su.project_id = sd.id
        where su.id = #{userId}
    </select>

    <select id="getProjectClassifyByRoleId" resultMap="BaseResultMap">
        select sd.*
        from sys_role_project_classify srpc
        left join sys_dict      sd on sd.id = srpc.project_classify_id
        <where>
            <if test="roleId != null and roleId != ''">
                srpc.role_id = #{roleId}
            </if>
        </where>
    </select>

    <select id="getProjectClassifyByUserId" resultMap="BaseResultMap">
        select sd.*
        from sys_role_project_classify srpc
        left join sys_user_role sur on sur.role_id = srpc.role_id
        left join sys_user su on su.id = sur.user_id
        left join sys_dict      sd on sd.id = srpc.project_classify_id
        <where>
            <if test="userId != null and userId != ''">
                su.id = #{userId}
            </if>
        </where>
        order by sd.sort
    </select>
</mapper>