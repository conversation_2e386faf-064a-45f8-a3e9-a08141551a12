package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.mapper.sys.SysDepartmentMapper;
import com.bjcj.xxgk.mapper.sys.SysUserMapper;
import com.bjcj.xxgk.model.dto.sys.SysDepartmentDto;
import com.bjcj.xxgk.model.pojo.sys.SysDepartment;
import com.bjcj.xxgk.model.pojo.sys.SysUser;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/20 15:08 周二
 */
@Service
public class SysDepartmentService extends ServiceImpl<SysDepartmentMapper, SysDepartment> {

    @Resource
    SysUserMapper sysUserMapper;

    @Transactional(rollbackFor = Exception.class)
    public JsonResult add(SysDepartment sysDepartment) {
        if (StrUtil.isNotBlank(sysDepartment.getId())) {
            return JsonResult.error("新增接口，不允许传id");
        }
        return save(sysDepartment) ? JsonResult.success() : JsonResult.error();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult fix(SysDepartment sysDepartment) {
        if (StrUtil.isBlank(sysDepartment.getId())) {
            return JsonResult.error("id是必传的");
        }
        return updateById(sysDepartment) ? JsonResult.success() : JsonResult.error();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult del(String id) {

        // 查询部门有没有人员
        boolean exists = sysUserMapper.exists(
                Wrappers.<SysUser>lambdaQuery().eq(SysUser::getDepartmentId, id)
        );
        if (exists) {
            return JsonResult.error("该部门下有人员，不允许删除");

        }
        return removeById(id) ? JsonResult.success() : JsonResult.error();
    }

    public JsonResult showTree() {
        return executionTree(
                list(
                        Wrappers.<SysDepartment>lambdaQuery()
                                .eq(SysDepartment::getStatus, 1)
                )
        );
    }

    public JsonResult manageTree() {
        return executionTree(list());
    }

    //=============================================================private========================================
    private JsonResult executionTree(List<SysDepartment> list) {
        List<SysDepartmentDto> trees = list.stream()
                .map(item -> BeanUtil.copyProperties(item, SysDepartmentDto.class))
                .toList();
        List<SysDepartmentDto> collect = trees.stream()
                .filter(item -> (StrUtil.isBlank(item.getParentId())))
                .peek(item -> {
                    item.setChildren(getChildren(item, trees));
                })
                .sorted(Comparator.comparing(SysDepartment::getSort))
                .sorted(Comparator.comparing(SysDepartment::getCreateTime))
                .collect(Collectors.toList());
        return JsonResult.success(collect);
    }


    private static List<SysDepartmentDto> getChildren(SysDepartmentDto root, List<SysDepartmentDto> all) {
        return all.stream()
                .filter(item -> StrUtil.equals(item.getParentId(), root.getId()))
                .peek(item -> item.setChildren(getChildren(item, all)))
                .collect(Collectors.toList());
    }
}
