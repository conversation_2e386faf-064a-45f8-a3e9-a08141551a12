package com.bjcj.xxgk;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;

/**
 * <AUTHOR>
 * @date 2024/8/15 17:50
 */
@SpringBootApplication
@MapperScan("com.bjcj.xxgk.mapper")
@Slf4j
public class PerformanceAppraisalApplication {

    public static void main(String[] args) throws Exception {
        ConfigurableApplicationContext application = SpringApplication.run(PerformanceAppraisalApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" :  property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application xxgk is running! Access URLs:\n\t" +
                        "Doc: \t\thttp://localhost:" + port + path + "/doc.html" + "\n\t" +
                        "Local: \t\thttp://localhost:" + port + path + "\n\t" +
                        "External: \thttp://" + ip + ":" + port + path + "\n\t" +
                        "------------------------------------------------------------");
    }

}
