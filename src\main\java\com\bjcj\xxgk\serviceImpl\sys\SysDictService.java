package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.constant.XxgkConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.mapper.sys.SysDictMapper;
import com.bjcj.xxgk.mapper.sys.SysDictTypeMapper;
import com.bjcj.xxgk.model.dto.GroupDto;
import com.bjcj.xxgk.model.dto.sys.SysDictDto;
import com.bjcj.xxgk.model.pojo.sys.SysDict;
import com.bjcj.xxgk.model.pojo.sys.SysDictType;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2024/8/29 16:15 周四
 */
@Service
public class SysDictService extends ServiceImpl<SysDictMapper, SysDict> {

    @Resource
    SysDictTypeMapper sysDictTypeMapper;

    @Resource
    SysDictMapper sysDictMapper;

    public JsonResult dictDataList(String dictCode, String dictLabel, Short status, String groupName) {

        // 查询字典类型
        SysDictType sysDictType = sysDictTypeMapper.selectOne(
                Wrappers.<SysDictType>lambdaQuery()
                        .eq(SysDictType::getCode, dictCode)
        );

        return getDictByCode(dictCode, dictLabel, status, groupName, sysDictType);
    }

    public JsonResult getDictByCode(String code) {
         return getDictByCode(
                 code, null, XxgkConstant.ENABLE, null,
                 sysDictTypeMapper.selectOne(Wrappers.<SysDictType>lambdaQuery().eq(SysDictType::getCode, code))
         );

    }

    //=====================================================私有方法===================================================


    private JsonResult getDictByCode(String dictCode, String dictLabel, Short status, String groupName, SysDictType sysDictType) {
        switch (sysDictType.getType()) {
            case NORMAL -> {
                return JsonResult.success(sysDictMapper.selectList(
                        Wrappers.<SysDict>lambdaQuery()
                                .eq(SysDict::getCode, dictCode)
                                .eq(StrUtil.isNotBlank(dictLabel), SysDict::getLabel, dictLabel)
                                .eq(ObjUtil.isNotNull(status), SysDict::getStatus, status)
                                .orderByAsc(SysDict::getSort)
                                .orderByDesc(SysDict::getCreateTime)
                ));
            }
            case CASCADE -> {
                return dictTree(dictCode, dictLabel, status, groupName);
            }
            case GROUP -> {
                return dictGroup(dictCode, dictLabel, status, groupName);
            }
        }
        return JsonResult.success();
    }

    private JsonResult dictGroup(String dictCode, String dictLabel, Short status, String groupName) {
        Map<String, List<SysDict>> groupList = list(
                Wrappers.<SysDict>lambdaQuery()
                        .eq(SysDict::getCode, dictCode)
                        .eq(StrUtil.isNotBlank(dictLabel), SysDict::getLabel, dictLabel)
                        .eq(StrUtil.isNotBlank(groupName), SysDict::getGroupName, groupName)
                        .eq(ObjUtil.isNotNull(status), SysDict::getStatus, status)
        ).stream()
                .sorted(Comparator.comparing(SysDict::getSort))
                .sorted(Comparator.comparing(SysDict::getCreateTime).reversed())
                .collect(groupingBy(SysDict::getGroupName));

        ArrayList<GroupDto<SysDict>> res = Lists.newArrayList();
        for (String s : groupList.keySet()) {
            res.add(new GroupDto<SysDict>(s, groupList.get(s)));
        }
        return JsonResult.success(res);
    }

    private JsonResult dictTree(String dictCode, String dictLabel, Short status, String groupName) {

        // 1. 查询全部字典
        List<SysDictDto> allSysDicts = list(
                Wrappers.<SysDict>lambdaQuery()
                        .eq(SysDict::getCode, dictCode)
                        .eq(StrUtil.isNotBlank(dictLabel), SysDict::getLabel, dictLabel)
                        .eq(ObjUtil.isNotNull(status), SysDict::getStatus, status)
        ).stream().map(item -> BeanUtil.copyProperties(item, SysDictDto.class))
                .sorted(Comparator.comparing(SysDict::getSort))
                .sorted(Comparator.comparing(SysDict::getCreateTime).reversed())
                .toList();
        // 2. 构建树
        List<SysDictDto> collect = allSysDicts.stream()
                .filter(item -> StrUtil.isBlank(item.getParentId()))
                .peek(item -> {
                    item.setChildren(getChildren(item, allSysDicts));
                })
                .collect(Collectors.toList());
        return JsonResult.success(collect);
    }

    private static List<SysDictDto> getChildren(SysDictDto root, List<SysDictDto> all) {
        return all.stream()
                .filter(item -> Objects.equals(item.getParentId(), root.getId()))
                .peek(item -> item.setChildren(getChildren(item, all)))
                .collect(Collectors.toList());
    }

    public JsonResult addOrFix(SysDict sysDict) {
        SysDict source = getOne(Wrappers.<SysDict>lambdaQuery()
                .eq(SysDict::getCode, sysDict.getCode())
                .eq(SysDict::getIsDefault, true));

        // 新增情况
        if (StrUtil.isBlank(sysDict.getId())) {
            if (ObjUtil.isNotEmpty(source) && sysDict.getIsDefault()) {
                return JsonResult.error("默认值已经存在");
            }
        } else {
            if (ObjUtil.isNotEmpty(source) && !source.getId().equals(sysDict.getId()) && sysDict.getIsDefault()) {
                return JsonResult.error("默认值已经存在");
            }
        }

        return saveOrUpdate(sysDict) ? JsonResult.success() : JsonResult.error("操作失败");
    }

    public JsonResult del(String id) {

        // 查询这个字典
        SysDict sysDict = getById(id);
        // 判断是否为空，而且这个code下不能是默认值
        if (ObjUtil.isNotEmpty(sysDict) && sysDict.getIsDefault()) {
            return JsonResult.error("默认值不能删除");
        }
        return JsonResult.success();
    }

    public JsonResult getDictById(String id) {
        // 查询这个字典
        return JsonResult.success(getById(id));
    }
}
