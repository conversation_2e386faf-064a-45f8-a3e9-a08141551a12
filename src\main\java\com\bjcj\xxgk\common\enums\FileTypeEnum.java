package com.bjcj.xxgk.common.enums;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 文件类型
 * <AUTHOR>
 * @date 2021-10-26 10:12
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Schema(description="文件类型")
public enum FileTypeEnum {
    
    AVATAR("头像", "avatar"),
    IMAGE("图片", "image"),
    VIDEO("视频", "video"),
    APK("安装包", "apk"),
    EXCEL("excel文件", "excel"),
    WORD("word文件", "word"),
    FILE("普通文件", "file"),
    PACKAGE("压缩包", "package"),
    RESULT("成果包", "result"),
    TEMPLATE("模板", "template"),
    ONEMAP("一张图文件", "onemap"),
    EVALUATION_REPORT("评估报告", "evaluation_report"),
    TXT("文本", "txt"),
    CAD("CAD文件", "cad"),
    TEST("测试", "test"),
    SHP("shp文件", "shp"),
    PDF("pdf文件", "pdf"),
    ;
    
    private String name;

    private String code;

    public static FileTypeEnum of(String code) {
        for (FileTypeEnum fileTypeEnum : values()) {
            if (fileTypeEnum.code.equals(code)) {
                return fileTypeEnum;
            }
        }
        return null;
    }

    public static FileTypeEnum ofSuf(String code) {
        if (StrUtil.equalsIgnoreCase(code, "xlsx") || StrUtil.equalsIgnoreCase(code, "xls")) {
            return EXCEL;
        }
        if (StrUtil.equalsIgnoreCase(code, "dwg") || StrUtil.equalsIgnoreCase(code, "dxf")) {
            return CAD;
        }
        if (StrUtil.equalsIgnoreCase(code, "txt")) {
            return TXT;
        }
        if (StrUtil.equalsIgnoreCase(code, "zip")) {
            return PACKAGE;
        }
        if (StrUtil.equalsIgnoreCase(code, "shp")) {
            return SHP;
        }
        return null;
    }
}
