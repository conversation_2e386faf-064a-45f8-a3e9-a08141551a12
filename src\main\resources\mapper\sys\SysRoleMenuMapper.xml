<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysRoleMenuMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysRoleMenu">
        <!--@Table sys_role_menu-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="menu_id" jdbcType="VARCHAR" property="menuId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, role_id, menu_id
    </sql>


</mapper>