package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.common.constant.XxgkConstant;
import com.bjcj.xxgk.common.constant.OperaLogConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.dto.sys.SysRolePermissionDto;
import com.bjcj.xxgk.model.dto.sys.SysRoleProjectClassifyDto;
import com.bjcj.xxgk.model.pojo.sys.SysRole;
import com.bjcj.xxgk.serviceImpl.sys.SysRoleService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/8/20 18:36
 */
@SaCheckLogin
@RestController
@RequestMapping("/role")
@Tag(name = "03角色")
@Validated
public class RoleController {
    @Resource
    private SysRoleService sysRoleService;

    @Operation(summary = "角色分页列表", description = "角色分页列表")
    @ApiOperationSupport(order = 1)
    @GetMapping
    public JsonResult rolePage(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
        return JsonResult.success(sysRoleService.page(
                new Page<>(pageNum, pageSize),
                Wrappers.<SysRole>lambdaQuery().orderByAsc(SysRole::getSort).orderByDesc(SysRole::getCreateTime)
        ));
    }

    @Operation(summary = "展示页面角色列表", description = "展示页面角色列表")
    @ApiOperationSupport(order = 1)
    @GetMapping("/show")
    public JsonResult showList() {
        return JsonResult.success(sysRoleService.list(
                Wrappers.<SysRole>lambdaQuery()
                        .eq(SysRole::getStatus, XxgkConstant.ENABLE)
                        .orderByAsc(SysRole::getSort).orderByDesc(SysRole::getCreateTime)
        ));
    }

    @Operation(summary = "角色回显", description = "角色回显")
    @ApiOperationSupport(order = 2)
    @GetMapping("/{id}")
    public JsonResult role(@PathVariable("id") String id) {
        return JsonResult.success(sysRoleService.getById(id));
    }

    @SaCheckPermission("system:role:add")
    @PostMapping
    @Operation(summary = "新增角色", description = "新增角色")
    @ApiOperationSupport(order = 3)
    @OperaLog(operaModule = "新增角色",operaType = OperaLogConstant.CREATE)
    public JsonResult add(@Validated @RequestBody SysRole sysrole) {
        return sysRoleService.add(sysrole);
    }

    @SaCheckPermission("system:role:edit")
    @PutMapping
    @Operation(summary = "编辑角色", description = "编辑角色")
    @ApiOperationSupport(order = 4)
    @OperaLog(operaModule = "编辑角色",operaType = OperaLogConstant.UPDATE)
    public JsonResult fix(@Validated @RequestBody SysRole sysrole) {
        return sysRoleService.fix(sysrole);
    }

    @SaCheckPermission("system:role:delete")
    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色", description = "删除角色")
    @ApiOperationSupport(order = 5)
    @OperaLog(operaModule = "删除角色",operaType = OperaLogConstant.DELETE)
    public JsonResult delete(@PathVariable("id") String id) {
        return sysRoleService.del(id);
    }

    @SaCheckPermission("system:role:viewuser")
    @GetMapping("/user/{roleId}")
    @Operation(summary = "查看角色拥有的用户", description = "查看角色拥有的用户")
    @ApiOperationSupport(order = 6)
    public JsonResult roleBelowUser(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize,
                                    @PathVariable("roleId") String roleId) {
        return sysRoleService.roleBelowUser(new Page<>(pageNum, pageSize), roleId);
    }

    @SaCheckPermission("system:role:permission")
    @GetMapping("/menu/{roleId}")
    @Operation(summary = "查看角色拥有的菜单", description = "查看角色拥有的菜单")
    @ApiOperationSupport(order = 7)
    public JsonResult roleBelowMenu(@PathVariable("roleId") String roleId) {
        return sysRoleService.roleBelowMenu(roleId);
    }


    @SaCheckPermission("system:role:permission:edit")
    @PostMapping("/menu")
    @Operation(summary = "修改角色的权限", description = "修改角色的权限")
    @ApiOperationSupport(order = 8)
    @OperaLog(operaModule = "修改角色的权限",operaType = OperaLogConstant.UPDATE)
    public JsonResult roleFixMenu(@RequestBody SysRolePermissionDto sysRolePermissionDto) {
        return sysRoleService.roleFixMenu(sysRolePermissionDto);
    }

    @SaCheckPermission("system:role:project:classify")
    @GetMapping("/project/classify/{roleId}")
    @Operation(summary = "查看角色拥有的项目类型", description = "查看角色拥有的项目类型")
    @ApiOperationSupport(order = 9)
    public JsonResult roleBelowProjectClassify(@PathVariable("roleId") String roleId) {
        return sysRoleService.roleBelowProjectClassify(roleId);
    }


    @SaCheckPermission("system:role:project:classify:edit")
    @PostMapping("/project/classify")
    @Operation(summary = "修改角色的项目类型", description = "修改角色的项目类型")
    @ApiOperationSupport(order = 10)
    @OperaLog(operaModule = "修改角色的权限",operaType = OperaLogConstant.UPDATE)
    public JsonResult roleFixProjectClassify(@RequestBody SysRoleProjectClassifyDto sysRoleProjectClassifyDto) {
        return sysRoleService.roleFixProjectClassify(sysRoleProjectClassifyDto);
    }

}
