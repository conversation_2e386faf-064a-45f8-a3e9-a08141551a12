<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysJobWeightMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysJobWeight">
        <!--@mbg.generated-->
        <!--@Table sys_job_weight-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="job_id" jdbcType="VARCHAR" property="jobId"/>
        <result column="judge" jdbcType="VARCHAR" property="judge"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="judge1" jdbcType="VARCHAR" property="judge1"/>
        <result column="amount" jdbcType="VARCHAR" property="amount"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, job_id, judge, weight, judge1, amount
    </sql>

    <select id="selectbdfUserIds" resultType="java.lang.String">
        <!--        select id from sys_user as su where su.id in ( select user_id from sys_user_job as suj where suj.job_id in( select job_id from sys_job_weight as sjw where sjw.judge=#{dfrJobId}) ) and su.is_rate=true-->
        SELECT su.id
        FROM sys_user AS              su
        left join scoring_process_log spl on su.id = spl.bkh_user_id
        WHERE su.ID IN (SELECT id
                        FROM sys_user AS su2
                        WHERE su2.job_id IN (SELECT job_id FROM sys_job_weight AS sjw WHERE sjw.judge = #{dfrJobId}))
          AND su.is_rate = TRUE
          and (spl.submit_status = false or spl.submit_status is null)
    </select>

    <select id="selectbdfUserIdsNew" resultType="java.lang.String">
        SELECT su.id
        FROM
        sys_user AS su
        left join scoring_process_log spl on su.id=spl.bkh_user_id
        WHERE
        su.ID IN ( SELECT id FROM sys_user AS su2 WHERE su2.job_id IN ( SELECT job_id FROM sys_job_weight AS sjw WHERE sjw.judge =#{dfrJobId} ) )
        AND su.is_rate = TRUE and su.is_del=false
    </select>
</mapper>