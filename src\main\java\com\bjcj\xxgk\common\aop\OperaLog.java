package com.bjcj.xxgk.common.aop;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-10-22 09:51:09
 */
@Target(ElementType.METHOD) //注解放置的目标位置,METHOD是可注解在方法级别上
@Retention(RetentionPolicy.RUNTIME) //注解在哪个阶段执行
@Documented
public @interface OperaLog {

    /**
     * 操作模块
     */
    String operaModule() default "";

    /**
     * 操作类型
     */
    String operaType() default "";

    /**
     * 操作说明
     */
    String operaDesc() default "";
}
