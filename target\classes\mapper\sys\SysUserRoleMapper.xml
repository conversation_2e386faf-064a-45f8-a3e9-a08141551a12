<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysUserRole">
    <!--@mbg.generated-->
    <!--@Table sys_user_role-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, role_id
  </sql>
</mapper>