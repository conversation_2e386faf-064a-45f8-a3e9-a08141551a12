package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.http.HttpRequest;
import com.bjcj.xxgk.common.properties.XxgkProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-27 11:51 周五
 */
@Service
@Slf4j
public class MessageService {

    @Resource
    XxgkProperties xxgkProperties;

    public void sendOfPump(String mobile, String message) {

        send(mobile, message, xxgkProperties.getJvhe().getMessage().getTplIds().get(0));
    }

    public void sendOfEvaluate(String mobile, String message) {

        send(mobile, message, xxgkProperties.getJvhe().getMessage().getTplIds().get(1));
    }

    public void sendOfPump(List<String> mobiles, String message) {

        for (String mobile : mobiles) {
            send(mobile, message, xxgkProperties.getJvhe().getMessage().getTplIds().get(1));
        }
    }

    private void send(String mobile, String message, Integer tplId) {

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("mobile", mobile);
        paramMap.put("tpl_id", tplId);
        paramMap.put("vars", message);
        paramMap.put("key", xxgkProperties.getJvhe().getMessage().getAk());

        String result2 = HttpRequest.post(xxgkProperties.getJvhe().getMessage().getUrl())
                .contentType("application/x-www-form-urlencoded")
                .form(paramMap)
                .timeout(20000)
                .execute().body();

        log.info("短信发送结果通知：：{}", result2);
    }

}
