# 文件发布管理API使用示例

## 前置条件

1. 确保数据库中已创建`file_release`表
2. 用户已登录系统（所有API都需要认证）
3. 服务器已启动并运行在相应端口

## 使用示例

### 1. 创建发布内容

```bash
# 使用curl创建新的发布内容
curl -X POST "http://localhost:8080/api/file-release" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "instanceId": "INST_001",
    "newsType": "系统公告",
    "fileHeader": "系统维护通知",
    "releaseUser": "系统管理员",
    "status": "已发布",
    "details": "系统将于本周末进行例行维护，维护期间可能影响部分功能的使用。"
  }'
```

### 2. 查询发布内容列表

```bash
# 查询第一页，每页10条记录
curl -X GET "http://localhost:8080/api/file-release?current=1&size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 按类型和状态过滤查询
curl -X GET "http://localhost:8080/api/file-release?current=1&size=10&newsType=系统公告&status=已发布" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 按时间范围查询
curl -X GET "http://localhost:8080/api/file-release?current=1&size=10&releaseStartTime=2024-12-01T00:00:00&releaseEndTime=2024-12-31T23:59:59" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 获取单个发布内容详情

```bash
# 根据ID获取详情
curl -X GET "http://localhost:8080/api/file-release/1234567890123456789" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 4. 更新发布内容

```bash
# 更新发布内容的标题和状态
curl -X PUT "http://localhost:8080/api/file-release/1234567890123456789" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "fileHeader": "更新后的系统维护通知",
    "status": "已撤回",
    "details": "维护计划已调整，具体时间另行通知。"
  }'
```

### 5. 删除发布内容

```bash
# 删除指定ID的发布内容
curl -X DELETE "http://localhost:8080/api/file-release/1234567890123456789" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## JavaScript/前端使用示例

### 使用fetch API

```javascript
// 创建发布内容
async function createFileRelease(data) {
  try {
    const response = await fetch('/api/file-release', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('创建成功');
      return result;
    } else {
      console.error('创建失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 查询发布内容列表
async function getFileReleaseList(params) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const response = await fetch(`/api/file-release?${queryString}`, {
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      console.error('查询失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 更新发布内容
async function updateFileRelease(id, data) {
  try {
    const response = await fetch(`/api/file-release/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('更新成功');
      return result;
    } else {
      console.error('更新失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 删除发布内容
async function deleteFileRelease(id) {
  try {
    const response = await fetch(`/api/file-release/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      }
    });
    
    const result = await response.json();
    if (result.code === 200) {
      console.log('删除成功');
      return result;
    } else {
      console.error('删除失败:', result.msg);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 获取认证token的辅助函数
function getToken() {
  // 这里应该从localStorage、sessionStorage或其他地方获取实际的token
  return localStorage.getItem('authToken');
}
```

### 使用示例

```javascript
// 创建新发布内容
const newRelease = {
  instanceId: "INST_001",
  newsType: "通知公告",
  fileHeader: "重要系统更新通知",
  releaseUser: "管理员",
  status: "草稿",
  details: "系统将在下周进行重要功能更新，请关注相关通知。"
};

createFileRelease(newRelease);

// 查询发布内容列表
const queryParams = {
  current: 1,
  size: 10,
  newsType: "通知公告",
  status: "已发布"
};

getFileReleaseList(queryParams).then(data => {
  console.log('查询结果:', data);
});

// 更新发布内容
const updateData = {
  status: "已发布",
  details: "更新后的详细内容"
};

updateFileRelease("1234567890123456789", updateData);

// 删除发布内容
deleteFileRelease("1234567890123456789");
```

## 常见问题和解决方案

### 1. 认证失败
确保请求头中包含有效的认证token：
```javascript
headers: {
  'Authorization': `Bearer ${token}`
}
```

### 2. 参数验证失败
检查必填字段是否都已提供，字符串长度是否超过限制。

### 3. 时间格式问题
时间字段应使用ISO 8601格式，例如：`2024-12-25T10:00:00`

### 4. 分页参数错误
确保`current`和`size`参数都大于0。

## 集成建议

1. **错误处理**: 始终检查响应的`code`字段来判断操作是否成功
2. **加载状态**: 在前端显示加载状态，提升用户体验
3. **数据验证**: 在发送请求前进行客户端验证
4. **缓存策略**: 对于查询操作可以考虑适当的缓存策略
5. **分页处理**: 实现合适的分页组件来处理大量数据
