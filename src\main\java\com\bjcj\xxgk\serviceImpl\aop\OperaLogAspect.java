package com.bjcj.xxgk.serviceImpl.aop;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.alibaba.fastjson2.JSON;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.mapper.sys.SysUserMapper;
import com.bjcj.xxgk.model.pojo.sys.SysErrorLog;
import com.bjcj.xxgk.model.pojo.sys.SysOperaLog;
import com.bjcj.xxgk.serviceImpl.sys.SysErrorLogService;
import com.bjcj.xxgk.serviceImpl.sys.SysOperaLogService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 切面处理类，操作日志记录处理
 *
 * <AUTHOR>
 * @date 2024-10-22 09:56:02
 */
@Aspect
@Component
@Slf4j
public class OperaLogAspect {


    @Resource
    SysOperaLogService sysOperaLogService;

    @Resource
    SysUserMapper sysUserMapper;

    @Resource
    SysErrorLogService sysErrorLogService;


    /**
     * 统计请求的处理时间
     */
    ThreadLocal<Long> startTime = new ThreadLocal<>();


    /**
     * 设置操作日志切入点 记录操作日志 在注解的位置切入代码
     */
    @Pointcut("@annotation(com.bjcj.xxgk.common.aop.OperaLog)")
    public void operaLogPointCut() {

    }

    @Before("operaLogPointCut()")
    public void doBefore() {
        // 接收到请求，记录请求开始时间
        startTime.set(System.currentTimeMillis());
    }

    /**
     * 正常返回通知，拦截用户操作日志，连接点正常执行完成后执行， 如果连接点抛出异常，则不会执行
     *
     * @param joinPoint 切入点
     * @param keys      返回结果
     */
    @AfterReturning(value = "operaLogPointCut()", returning = "keys")
    public void saveOperaLog(JoinPoint joinPoint, Object keys) {

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

        try {
            SysOperaLog operaLog = new SysOperaLog();

            // 从切面织入点处通过反射机制获取织入点处的方法
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            // 获取切入点所在的方法
            Method method = signature.getMethod();
            // 获取操作
            OperaLog opLog = method.getAnnotation(OperaLog.class);
            if (opLog != null) {
                String operaModule = opLog.operaModule();
                String operaType = opLog.operaType();
                String operaDesc = opLog.operaDesc();
                operaLog.setModule(operaModule);
                operaLog.setType(operaType);
                operaLog.setDesc(operaDesc);

            }
            // 获取请求的类名
            String className = joinPoint.getTarget().getClass().getName();
            // 获取请求的方法名
            String methodName = method.getName();
            methodName = className + "." + methodName;

            // 请求方法
            operaLog.setMethod(methodName);

            operaLog.setReqType(request.getMethod());

            // 请求的参数
            String params = "";
            if (StrUtil.equals(request.getMethod(), "GET")
                    || StrUtil.equals(request.getMethod(), "DELETE")) {

                params = JSON.toJSONString(getRequestParam(request));
            }
            if (StrUtil.equals(request.getMethod(), "POST")) {

                params = JSON.toJSONString(joinPoint.getArgs()[0]);
            }
            operaLog.setReqParam(params);

            operaLog.setUserId(String.valueOf(StpUtil.getLoginId()));
            operaLog.setUserName(this.sysUserMapper.selectById(String.valueOf(StpUtil.getLoginId())).getUsername());
            operaLog.setIp(JakartaServletUtil.getClientIP(request, "Cdn-Src-Ip"));
            operaLog.setUri(request.getRequestURI());
            operaLog.setResParam(JSON.toJSONString(keys));
            operaLog.setTakeUpTime(System.currentTimeMillis() - startTime.get());

            sysOperaLogService.save(operaLog);

        } catch (Exception e) {
            log.error("操作日志生成错误：", e);
        }

    }


    /**
     * <h2>异常返回通知，用于拦截异常日志信息 连接点抛出异常后执行</h2>
     *
     * @param joinPoint:
     * @param e:
     * @return void
     * <AUTHOR>
     * @date 2023/12/8 11:00
     */
    @AfterThrowing(pointcut = "operaLogPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable e) {

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

        try {
            // 从切面织入点处通过反射机制获取织入点处的方法
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();

            // 获取切入点所在的方法
            Method method = signature.getMethod();

            // 获取请求的类名
            String className = joinPoint.getTarget().getClass().getName();

            // 请求的参数
            String params = "";
            if (StrUtil.equals(request.getMethod(), "GET")
                    || StrUtil.equals(request.getMethod(), "DELETE")) {

                params = JSON.toJSONString(getRequestParam(request));
            }
            if (StrUtil.equals(request.getMethod(), "POST")) {

                params = JSON.toJSONString(joinPoint.getArgs()[0]);
            }
            sysErrorLogService.save(
                    SysErrorLog.builder()
                            .reqParam(params)
                            .method(className + "." + method.getName())
                            .name(e.getClass().getName())
                            .message(stackTraceToString(e.getClass().getName(), e.getMessage(), e.getStackTrace()))
                            .userId(String.valueOf(StpUtil.getLoginId()))
                            .userName(this.sysUserMapper.selectById(String.valueOf(StpUtil.getLoginId())).getUsername())
                            .uri(request.getRequestURI())
                            .type(request.getMethod())
                            .ip(JakartaServletUtil.getClientIP(request, "Cdn-Src-Ip"))
                            .build()
            );
        } catch (Exception e2) {
            log.error("异常日志生成错误：", e2);
        }
    }


    /**
     * <h2>获取get请求参数</h2>
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2023/12/1 9:18
     */
    private Map<String, String> getRequestParam(HttpServletRequest request) {
        // 返回体
        HashMap<String, String> res = new HashMap<>();

        // 获取 GET 请求参数
        Map<String, String[]> parameterMap = request.getParameterMap();

        // 遍历参数Map
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String paramName = entry.getKey();
            String[] paramValues = entry.getValue();

            if (paramValues != null && paramValues.length > 0) {
                res.put(paramName, paramValues[0]);
            }
        }
        return res;
    }

    /**
     * <h2>转换异常信息为字符串</h2>
     *
     * @param exceptionName:
     * @param exceptionMessage:
     * @param elements:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/12/8 10:56
     */
    public String stackTraceToString(String exceptionName, String exceptionMessage, StackTraceElement[] elements) {
        StringBuffer strbuff = new StringBuffer();
        for (StackTraceElement stet : elements) {
            strbuff.append(stet + "<br/>");
        }
        String message = exceptionName + ":" + exceptionMessage + "<br/>" + strbuff.toString();
        return message;
    }


}
