package com.bjcj.xxgk.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型
 * <AUTHOR>
 * @date 2024/8/20 18:03 周二
 */
@Getter
@AllArgsConstructor
public enum DeviceEnum {

    /**
     * 电脑
     */
    PC("pc"),

    /**
     * 安卓
     */
    ANDROID("android"),

    /**
     * 苹果
     */
    IOS("ios"),

    /**
     * 手机端
     */
    APP("app");

    @EnumValue
    @JsonValue
    private final String code;
}
