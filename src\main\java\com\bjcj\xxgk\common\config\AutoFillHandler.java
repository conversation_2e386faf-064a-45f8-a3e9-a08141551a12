package com.bjcj.xxgk.common.config;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.bjcj.xxgk.mapper.sys.SysUserMapper;
import com.bjcj.xxgk.model.pojo.sys.SysUser;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/20 23:35 周一
 */
@Slf4j
@Component
public class AutoFillHandler implements MetaObjectHandler {

    @Resource
    SysUserMapper sysUserMapper;

    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("start insert fill ....");
        this.strictInsertFill(metaObject, "createTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
        this.strictInsertFill(metaObject, "operator", this::getCurrentUserName, String.class);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("start update fill ....");
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
    }

    private String getCurrentUserName() {
        SysUser sysUser = sysUserMapper.selectById(StpUtil.getLoginIdAsString());
        if (ObjUtil.isNotEmpty(sysUser)) {
            return sysUser.getUsername();
        }
        return "system";
    }

}
