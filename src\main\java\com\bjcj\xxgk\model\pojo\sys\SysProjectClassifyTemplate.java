package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-04-10 17:25 周四
 */
@Schema
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_project_classify_template")
public class SysProjectClassifyTemplate {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    /**
     * 项目类型id
     */
    @TableField(value = "project_classify")
    @Schema(description = "项目类型id")
    private String projectClassify;

    /**
     * 模板名字
     */
    @TableField(value = "\"template\"")
    @Schema(description = "模板名字")
    private String template;

    /**
     * 项目名字
     */
    @TableField(value = "project_name")
    @Schema(description = "项目名字")
    private String projectName;
}