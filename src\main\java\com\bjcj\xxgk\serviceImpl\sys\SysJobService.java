package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.utils.SnowflakeUtil;
import com.bjcj.xxgk.mapper.sys.SysJobMapper;
import com.bjcj.xxgk.mapper.sys.SysJobWeightMapper;
import com.bjcj.xxgk.mapper.sys.SysUserBranchDepMapper;
import com.bjcj.xxgk.mapper.sys.SysUserMapper;
import com.bjcj.xxgk.model.dto.sys.SysJobDto;
import com.bjcj.xxgk.model.pojo.sys.SysJob;
import com.bjcj.xxgk.model.pojo.sys.SysJobWeight;
import com.bjcj.xxgk.model.pojo.sys.SysUser;
import com.bjcj.xxgk.model.pojo.sys.SysUserBranchDep;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/20 15:08 周二
 */
@Service
public class SysJobService extends ServiceImpl<SysJobMapper, SysJob> {

    @Resource
    SysJobMapper sysJobMapper;

    @Resource
    SysJobWeightMapper sysJobWeightMapper;

    @Resource
    SysUserMapper sysUserMapper;

    @Resource
    SysUserBranchDepMapper sysUserBranchDepMapper;


    @Transactional(rollbackFor = Exception.class)
    public JsonResult add(SysJobDto sysJobDto) {
        if (StrUtil.isNotBlank(sysJobDto.getId())) {
            return JsonResult.error("新增接口，不允许传id");
        }

        // 新增职务
        sysJobDto.setId(SnowflakeUtil.snowflakeId());
        save(BeanUtil.copyProperties(sysJobDto, SysJob.class));

        // 构建权重信息
        List<SysJobWeight> sysJobWeights = sysJobDto.getJobWeightList()
                .stream()
                .map(
                        item -> SysJobWeight.builder()
                                .jobId(sysJobDto.getId())
                                .judge(item.getJobId())
                                .judge1(item.getJobId())
                                .weight(item.getWeight())
                                .amount(item.getAmount())
                                .build()
                )
                .toList();
        sysJobWeightMapper.insert(sysJobWeights);

        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult fix(SysJobDto sysJobDto) {
        if (StrUtil.isBlank(sysJobDto.getId())) {
            return JsonResult.error("id是必传的");
        }

        // 1.修改职务
        updateById(BeanUtil.copyProperties(sysJobDto, SysJob.class));

        // 2.修改权重
        sysJobWeightMapper.delete(
                Wrappers.<SysJobWeight>lambdaQuery()
                        .eq(SysJobWeight::getJobId, sysJobDto.getId())
        );
        List<SysJobWeight> sysJobWeights = sysJobDto.getJobWeightList()
                .stream()
                .map(
                        item -> SysJobWeight.builder()
                                .jobId(sysJobDto.getId())
                                .judge(mapJob(item.getJobId()))
                                .judge1(item.getJobId())
                                .weight(item.getWeight())
                                .amount(item.getAmount())
                                .build()
                )
                .toList();
        sysJobWeightMapper.insert(sysJobWeights);

        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult del(String id) {

        // 查询职务有没有人员
        boolean exists = sysUserMapper.exists(
                Wrappers.<SysUser>lambdaQuery().eq(SysUser::getJobId, id)
        );
        if (exists) {
            return JsonResult.error("该职务下有人员，不允许删除");
        }

        // 1.删除职务
        removeById(id);

        // 2.删除权重
        sysJobWeightMapper.delete(
                Wrappers.<SysJobWeight>lambdaQuery()
                        .eq(SysJobWeight::getJobId, id)
        );
        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult jobWeight(String id) {

        SysJobDto jobWeight = sysJobMapper.getJobWeight(id);

        // 2.1 只修改副主任评价副主任时的人数
        jobWeight.getJobWeightList().forEach(
            item -> {
                if (StrUtil.equals(item.getJobId(), item.getReceiveId())) {
                    item.setAmount("2");
                }
            }
        );

        return JsonResult.success(jobWeight);
    }

    /**
     * <h2>getWeight</h2>
     * @param userId: 被打分人
     * @param assessorId:  打分人
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024-10-09 11:38
     */
    public String getWeight(String userId, String assessorId) {
        // 被考核人
        SysUser sysUser = sysUserMapper.selectById(userId);
        // 考核人
        SysUser assessor = sysUserMapper.selectById(assessorId);

        List<SysJobWeight> sysJobWeights = sysJobWeightMapper.selectList(
                Wrappers.<SysJobWeight>lambdaQuery()
                        .eq(SysJobWeight::getJobId, sysUser.getJobId())
                        .eq(SysJobWeight::getJudge, assessor.getJobId())
        );

        if (sysJobWeights.size() == 1) {
            return sysJobWeights.get(0).getWeight();
        } else {
            if (StrUtil.equals(assessor.getJobId(), "1830431506590466048")) {
                // 科长的情况，区分是 分管科长还是其他科长
                SysUserBranchDep sysUserBranchDep = sysUserBranchDepMapper.selectOne(
                        Wrappers.<SysUserBranchDep>lambdaQuery()
                                .eq(SysUserBranchDep::getUserId, userId)
                                .eq(SysUserBranchDep::getDepartmentId, assessor.getDepartmentId())
                );
                if (ObjUtil.isNotEmpty(sysUserBranchDep)) {
                    // 分管科长
                    return sysJobWeightMapper.selectOne(
                            Wrappers.<SysJobWeight>lambdaQuery()
                                    .eq(SysJobWeight::getJobId, sysUser.getJobId())
                                    .eq(SysJobWeight::getJudge1, "1830431580708012032")
                    ).getWeight();
                } else {
                    // 其他科长
                    return sysJobWeightMapper.selectOne(
                            Wrappers.<SysJobWeight>lambdaQuery()
                                    .eq(SysJobWeight::getJobId, sysUser.getJobId())
                                    .eq(SysJobWeight::getJudge1, "1830431653835702272")
                    ).getWeight();
                }
            } else if (StrUtil.equals(assessor.getJobId(), "1830431465402400768")) {
                // 副主任的情况，区分是 分管副主任还是其他副主任
                SysUserBranchDep sysUserBranchDep = sysUserBranchDepMapper.selectOne(
                        Wrappers.<SysUserBranchDep>lambdaQuery()
                                .eq(SysUserBranchDep::getUserId, assessorId)
                                .eq(SysUserBranchDep::getDepartmentId, sysUser.getDepartmentId())
                );
                if (ObjUtil.isNotEmpty(sysUserBranchDep)) {
                    // 分管副主任
                    return sysJobWeightMapper.selectOne(
                            Wrappers.<SysJobWeight>lambdaQuery()
                                    .eq(SysJobWeight::getJobId, sysUser.getJobId())
                                    .eq(SysJobWeight::getJudge1, "1830431774992367616")
                    ).getWeight();
                } else {
                    // 其他副主任
                    return sysJobWeightMapper.selectOne(
                            Wrappers.<SysJobWeight>lambdaQuery()
                                    .eq(SysJobWeight::getJobId, sysUser.getJobId())
                                    .eq(SysJobWeight::getJudge1, "1830431822488666112")
                    ).getWeight();
                }
            }
        }
        return null;
    }

    //========================================================private===============================================

    // 计算职位的人数
    private String calJobCount(String jobId) {
        // 获取职位的人数
        String count = String.valueOf(
                sysUserMapper.selectCount(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getJobId, jobId))
        );
        // 更新职位的人数
        sysJobMapper.update(Wrappers.<SysJob>lambdaUpdate().set(SysJob::getAmount, count).eq(SysJob::getId, jobId));
        return count;
    }

    // 把虚拟职位映射成具体职位
    private String mapJob(String jobId) {
        switch (jobId) {
            case "1830431653835702272", "1830431580708012032" -> {
                return "1830431506590466048";
            }
            case "1830431774992367616", "1830431822488666112" -> {
                return "1830431465402400768";
            }
        }
        return jobId;
    }

}
