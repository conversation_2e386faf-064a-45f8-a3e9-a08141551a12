package com.bjcj.xxgk.common.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import com.bjcj.xxgk.common.interceptor.RequestIdInterceptor;
import com.bjcj.xxgk.common.properties.XxgkProperties;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @date 2023/8/21 8:19
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    XxgkProperties XxgkProperties;

    /**
     * SpringBoot 静态资源配置
     *
     * @param registry 注册类
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/file/**")
                .addResourceLocations("file:" + XxgkProperties.getFile().getUpload());
        registry.addResourceHandler("/log/**")
                .addResourceLocations("file:" + XxgkProperties.getFile().getLog());
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        registry.addInterceptor(new SaInterceptor()).addPathPatterns("/**");
        registry.addInterceptor(new RequestIdInterceptor());
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                //是否发送Cookie
                .allowCredentials(true)
                //放行哪些原始域
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "options")
                .allowedHeaders("*")
                .exposedHeaders("*");
    }


}
