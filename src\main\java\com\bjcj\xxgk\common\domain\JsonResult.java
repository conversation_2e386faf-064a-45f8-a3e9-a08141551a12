package com.bjcj.xxgk.common.domain;

import java.util.HashMap;

/**
 * 操作消息提醒
 *
 * <AUTHOR>
 */
public class JsonResult extends HashMap<String, Object> {

    /**
     * 状态码
     */
    public static final String CODE_TAG = "code";

    /**
     * 返回内容
     */
    public static final String MSG_TAG = "msg";

    /**
     * 数据对象
     */
    public static final String DATA_TAG = "data";

    /**
     * 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。
     */
    public JsonResult() {
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态类型
     * @param msg  返回内容
     */
    public JsonResult(Code code, Message msg) {
        super.put(CODE_TAG, code.value());
        super.put(MSG_TAG, msg.message());
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态类型
     * @param msg  返回内容
     * @param data 数据对象
     */
    public JsonResult(Code code, Message msg, Object data) {
        super.put(CODE_TAG, code.value());
        super.put(MSG_TAG, msg.message());
        if (data != null) {
            super.put(DATA_TAG, data);
        }
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态类型
     * @param msg  返回内容
     * @param data 数据对象
     */
    public JsonResult(int code, String msg, Object data) {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
        if (data != null) {
            super.put(DATA_TAG, data);
        }
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static JsonResult success() {
        return JsonResult.success(Message.SUCCESS);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @return 成功消息
     */
    public static JsonResult success(Message msg) {
        return JsonResult.success(msg, null);
    }

    public static JsonResult success(String msg) {
        return JsonResult.build(Code.SUCCESS.value(), msg, null);
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static JsonResult success(Object data) {
        return JsonResult.success(Message.SUCCESS, data);
    }

    /**
     * 返回成功消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static JsonResult success(Message msg, Object data) {
        return new JsonResult(Code.SUCCESS, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @return
     */
    public static JsonResult error() {
        return JsonResult.error(Message.ERROR);
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static JsonResult error(Message msg) {
        return JsonResult.error(msg, null);
    }

    public static JsonResult error(String msg) {
        return JsonResult.build(Code.ERROR.value(), msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static JsonResult error(Message msg, Object data) {
        return new JsonResult(Code.ERROR, msg, data);
    }

    /**
     * 构建一个返回信息
     *
     * @param code:
     * @param msg:
     * @return com.tubond.dailymanager.common.domain.AjaxResult
     * <AUTHOR>
     * @date 2021-08-26 14:51
     */
    public static JsonResult build(int code, String msg) {
        return new JsonResult(code, msg, null);
    }

    /**
     * 构建一个返回信息
     *
     * @param code:
     * @param msg:
     * @return com.tubond.dailymanager.common.domain.AjaxResult
     * <AUTHOR>
     * @date 2021-08-26 14:51
     */
    public static JsonResult build(Code code, Message msg) {
        return new JsonResult(code, msg, null);
    }

    /**
     * 构建一个返回信息
     *
     * @param code:
     * @param msg:
     * @param data:
     * @return com.tubond.dailymanager.common.domain.AjaxResult
     * <AUTHOR>
     * @date 2021-08-26 14:45
     */
    public static JsonResult build(Code code, Message msg, Object data) {
        return new JsonResult(code, msg, data);
    }

    public static JsonResult build(int code, String msg, Object data) {
        return new JsonResult(code, msg, data);
    }
}
