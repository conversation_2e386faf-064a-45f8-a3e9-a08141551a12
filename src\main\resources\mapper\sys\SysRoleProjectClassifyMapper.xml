<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysRoleProjectClassifyMapper">
    <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysRoleProjectClassify">
        <!--@mbg.generated-->
        <!--@Table sys_role_project_classify-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="role_id" jdbcType="VARCHAR" property="roleId"/>
        <result column="project_classify_id" jdbcType="VARCHAR" property="projectClassifyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, role_id, project_classify_id
    </sql>

</mapper>