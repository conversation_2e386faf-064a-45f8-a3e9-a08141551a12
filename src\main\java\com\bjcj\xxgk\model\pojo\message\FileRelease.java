package com.bjcj.xxgk.model.pojo.message;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 文件发布实体类
 * <AUTHOR>
 * @date 2024/12/25
 */
@Schema(description = "文件发布表")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "file_release")
public class FileRelease {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField(value = "instance_id")
    @Schema(description = "实例ID")
    private String instanceId;

    @TableField(value = "news_type")
    @Schema(description = "新闻/消息类型")
    private String newsType;

    @TableField(value = "file_header")
    @Schema(description = "文件/消息标题")
    private String fileHeader;

    @TableField(value = "release_time")
    @Schema(description = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime releaseTime;

    @TableField(value = "release_user")
    @Schema(description = "发布者/作者")
    private String releaseUser;

    @TableField(value = "status")
    @Schema(description = "发布状态")
    private String status;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField(value = "details")
    @Schema(description = "详细内容")
    private String details;
}
