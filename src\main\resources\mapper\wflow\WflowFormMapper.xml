<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.wflow.WflowFormMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.wflow.WflowForm">
    <!--@mbg.generated-->
    <!--@Table wflow_form-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="instance_id" jdbcType="VARCHAR" property="instanceId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="form_items" jdbcType="VARCHAR" property="formItems" />
    <result column="form_config" jdbcType="VARCHAR" property="formConfig" />
    <result column="form_dir_id" jdbcType="VARCHAR" property="formDirId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, instance_id, material_name, form_items, form_config, form_dir_id
  </sql>

</mapper>