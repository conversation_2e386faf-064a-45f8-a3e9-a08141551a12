<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.wflow.WflowProjectMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.wflow.WflowProject">
    <!--@mbg.generated-->
    <!--@Table wflow_project-->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="instance_id" jdbcType="VARCHAR" property="instanceId" />
    <result column="process_def_name" jdbcType="VARCHAR" property="processDefName" />
    <result column="instance_name" jdbcType="VARCHAR" property="instanceName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="start_user_id" jdbcType="VARCHAR" property="startUserId" />
    <result column="start_user_name" jdbcType="VARCHAR" property="startUserName" />
    <result column="start_user_dept" jdbcType="VARCHAR" property="startUserDept" />
    <result column="form_data" jdbcType="VARCHAR" property="formData" />
    <result column="form_dir" jdbcType="VARCHAR" property="formDir" />
    <result column="progress" jdbcType="VARCHAR" property="progress" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, instance_id, process_def_name, instance_name, "status", start_user_id, start_user_name, 
    start_user_dept, form_data, form_dir, progress, start_time, remark
  </sql>

</mapper>