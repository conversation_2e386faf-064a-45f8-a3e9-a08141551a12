package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.properties.XxgkProperties;
import com.bjcj.xxgk.model.pojo.sys.SysWeather;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024-09-23 17:08 周一
 */
@Service
@Slf4j
public class ThirdApiService {

    @Resource
    XxgkProperties xxgkProperties;

    @Resource
    SysWeatherService sysWeatherService;

    public JsonResult dayWeather() {

        SysWeather weather = sysWeatherService.getOne(
                Wrappers.<SysWeather>lambdaQuery()
                        .eq(SysWeather::getDay, LocalDate.now())
        );

        String result;
        if (ObjUtil.isEmpty(weather)) {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("city", xxgkProperties.getArea());
            paramMap.put("key", xxgkProperties.getJvhe().getWeather().getAk());
            result = HttpUtil.get(xxgkProperties.getJvhe().getWeather().getUrl(), paramMap);
            sysWeatherService.save(SysWeather.builder().day(LocalDate.now()).json(result).build());
        } else {
            result = weather.getJson();
        }

        return JsonResult.success(JSON.parse(result));
    }
}
