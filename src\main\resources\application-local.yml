spring:
    datasource:
        driver-class-name: org.postgresql.Driver
#        url: *************************************
#        username: postgres
#        password: 123456
        url: jdbc:postgresql://************:5432/xxgk
        username: postgres
        password: cj@888
        type: com.zaxxer.hikari.HikariDataSource
        hikari:
            maximum-pool-size: 8
            minimum-idle: 4
            idle-timeout: 30000
            connection-timeout: 30000
            max-lifetime: 45000

    servlet:
        multipart:
            # 最大置总上传的数据大小 ：默认10M
            max-request-size: 1024MB
            # 最大上传单个文件大小：默认1M
            max-file-size: 1024MB
    jackson:
        time-zone: GMT+8
        date-format: java.text.SimpleDateFormat
    mvc:
        pathmatch:
            ## knife4j推荐的配置，不加或许会报错吧
            matching-strategy: ant_path_matcher
    web:
        resources:
            static-locations: classpath:/admin/
    data:
        redis:
            host: 127.0.0.1
            port: 6379
            database: 1
            timeout: 2000ms
            lettuce:
                pool:
                    # 连接池最大连接数
                    max-active: 20
                    # 连接池中的最小空闲连接
                    max-idle: 10
                    # 连接池最大阻塞等待时间(使用负数表示没有限制,单位ms)
                    max-wait: 3000

# sa-token配置
sa-token:
    # token 名称（同时也是 cookie 名称）
    token-name: authorization
    # token 有效期（单位：秒） 默认30天，-1 代表永久有效
    timeout: 2592000
    # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
    active-timeout: -1
    # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
    is-concurrent: true
    # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
    is-share: false
    # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
    token-style: simple-uuid
    # 是否输出操作日志
    is-log: true
    # token前缀
    token-prefix: Bearer

mybatis-plus:
    type-aliases-package: com.bjcj.xxgk.model
    global-config:
        banner: false
        db-config:
            logic-delete-field: isDel
            logic-delete-value: true
            logic-not-delete-value: false

xxgk:
    file:
        upload: D:/upload/xxgk/
        log: D:/project/logs/xxgk/
    check-captcha: false
    jvhe:
        weather:
            ak: 13ab66f0e10471ed3845d747dd7c400e
            url: http://apis.juhe.cn/simpleWeather/query
        message:
            ak: 89d068d00c94068c47a2c271b5e7d134
            url: http://v.juhe.cn/sms/send
            tpl-ids:
                - 265471
                - 267074
            trans-url: http://192.168.3.30/xxgk-admin/#/c
    default-password: 123456

logging:
    level:
        com.bjcj.xxgk.mapper: debug
        cn.dev33: debug


