package com.bjcj.xxgk.controller.message;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.aop.OperaLog;
import com.bjcj.xxgk.common.constant.OperaLogConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.dto.message.FileReleaseCreateRequest;
import com.bjcj.xxgk.model.dto.message.FileReleasePageRequest;
import com.bjcj.xxgk.model.dto.message.FileReleaseUpdateRequest;
import com.bjcj.xxgk.serviceImpl.message.FileReleaseService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 文件/消息发布管理控制器
 * 
 * <AUTHOR>
 * @date 2024/12/25
 */
@SaCheckLogin
@RestController
@RequestMapping("/api/file-release")
@Tag(name = "文件发布管理")
@Validated
public class lawsOperation {

    @Resource
    private FileReleaseService fileReleaseService;

    @Operation(summary = "创建新发布内容", description = "创建新的文件/消息发布内容")
    @ApiOperationSupport(order = 1)
    @OperaLog(operaModule = "文件发布管理", operaType = OperaLogConstant.CREATE)
    @PostMapping
    public JsonResult create(@Valid @RequestBody FileReleaseCreateRequest request) {
        return fileReleaseService.create(request);
    }

    @Operation(summary = "分页查询发布内容", description = "分页查询发布内容，支持按类型、状态、发布者等字段过滤")
    @ApiOperationSupport(order = 2)
    @GetMapping
    public JsonResult page(@Valid FileReleasePageRequest request) {
        Page<Object> page = new Page<>(request.getCurrent(), request.getSize());
        return fileReleaseService.page(page, request);
    }

    @Operation(summary = "根据ID获取发布内容", description = "根据ID获取单个发布内容详情")
    @ApiOperationSupport(order = 3)
    @GetMapping("/{id}")
    public JsonResult getById(@PathVariable String id) {
        return fileReleaseService.getById(id);
    }

    @Operation(summary = "更新发布内容", description = "更新现有的发布内容")
    @ApiOperationSupport(order = 4)
    @OperaLog(operaModule = "文件发布管理", operaType = OperaLogConstant.UPDATE)
    @PutMapping("/{id}")
    public JsonResult update(@PathVariable String id, @Valid @RequestBody FileReleaseUpdateRequest request) {
        return fileReleaseService.update(id, request);
    }

    @Operation(summary = "发布内容", description = "更改发布内容的状态为已发布")
    @ApiOperationSupport(order = 5)
    @OperaLog(operaModule = "文件发布管理", operaType = "发布")
    @PutMapping("/{id}/publish")
    public JsonResult publish(@PathVariable String id) {
        return fileReleaseService.publish(id);
    }

    @Operation(summary = "撤回发布", description = "更改发布内容的状态为已撤回")
    @ApiOperationSupport(order = 6)
    @OperaLog(operaModule = "文件发布管理", operaType = "撤回")
    @PutMapping("/{id}/withdraw")
    public JsonResult withdraw(@PathVariable String id) {
        return fileReleaseService.withdraw(id);
    }

    @Operation(summary = "更改发布状态", description = "更改发布内容的状态")
    @ApiOperationSupport(order = 7)
    @OperaLog(operaModule = "文件发布管理", operaType = "状态变更")
    @PutMapping("/{id}/status/{status}")
    public JsonResult changeStatus(@PathVariable String id, @PathVariable String status) {
        return fileReleaseService.changeStatus(id, status);
    }

    @Operation(summary = "删除发布内容", description = "根据ID删除发布内容")
    @ApiOperationSupport(order = 8)
    @OperaLog(operaModule = "文件发布管理", operaType = OperaLogConstant.DELETE)
    @DeleteMapping("/{id}")
    public JsonResult delete(@PathVariable String id) {
        return fileReleaseService.delete(id);
    }
}
