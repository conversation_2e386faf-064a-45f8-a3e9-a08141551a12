package com.bjcj.xxgk.model.dto.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/19 17:39 周二
 */
@Data
@Schema(description = "系统错误日志查询对象")
public class SysErrorLogDto {

    @Schema(description = "错误名称")
    private String errorName;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "ip")
    private String ip;

    @Schema(description = "请求类型")
    private String type;

    @Schema(description = "开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime endTime;

    @NotNull(message = "分页current不允许为空")
    @Schema(description = "当前页")
    private Long current;

    @NotNull(message = "分页size不允许为空")
    @Schema(description = "每页数量")
    private Long size;

}
