package com.bjcj.xxgk.controller.sys;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.model.pojo.sys.SysOperaLog;
import com.bjcj.xxgk.serviceImpl.sys.SysOperaLogService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-10-22 15:07
 */
@SaCheckLogin
@RestController
@RequestMapping("/operationLog")
@Tag(name = "操作日志")
public class OperationLogController {

    @Resource
    SysOperaLogService sysOperaLogService;

    @GetMapping("/logList")
    @Operation(summary = "列表", description = "列表")
    @ApiOperationSupport(order = 1)
    @Parameters({
            @Parameter(name = "pageNum", description = "页码", required = true),
            @Parameter(name = "pageSize", description = "每页数量", required = true),
            @Parameter(name = "begintime", description = "开始时间", required = false),
            @Parameter(name = "endtime", description = "结束时间", required = false),
            @Parameter(name = "username", description = "用户名", required = false)
    })
    public JsonResult loglist(@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize,
                           @RequestParam(value = "begintime", required = false) String begintime,
                           @RequestParam(value = "endtime", required = false) String endtime,
                           @RequestParam(value = "username", required = false) String username) {
        Page<SysOperaLog> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SysOperaLog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(Objects.nonNull(username), SysOperaLog::getUserName, username);
        if(Objects.nonNull(begintime) && Objects.nonNull(endtime)){
            lambdaQueryWrapper.between(SysOperaLog::getCreateTime, LocalDateTime.parse(begintime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), LocalDateTime.parse(endtime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        lambdaQueryWrapper.orderByDesc(SysOperaLog::getCreateTime);
        Page<SysOperaLog> page1 = this.sysOperaLogService.page(page, lambdaQueryWrapper);
        return JsonResult.success(page1);
    }
}
