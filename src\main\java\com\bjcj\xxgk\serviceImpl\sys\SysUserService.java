package com.bjcj.xxgk.serviceImpl.sys;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.constant.XxgkConstant;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.exception.BusinessException;
import com.bjcj.xxgk.common.properties.XxgkProperties;
import com.bjcj.xxgk.common.utils.SnowflakeUtil;
import com.bjcj.xxgk.mapper.sys.*;
import com.bjcj.xxgk.model.dto.sys.JobMoveDto;
import com.bjcj.xxgk.model.dto.sys.PasswordDto;
import com.bjcj.xxgk.model.dto.sys.SysUserDepJobProjectDto;
import com.bjcj.xxgk.model.dto.sys.SysUserDto;
import com.bjcj.xxgk.model.pojo.sys.*;
import com.bjcj.xxgk.model.qo.SysUserQo;
import com.bjcj.xxgk.model.vo.SysMenuVo;
import com.bjcj.xxgk.model.vo.SysUserVo;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/20 15:08 周二
 */
@Service
@Slf4j
public class SysUserService extends ServiceImpl<SysUserMapper, SysUser> {

    @Resource
    SysUserMapper sysUserMapper;

    @Resource
    SysUserRoleMapper sysUserRoleMapper;

    @Resource
    SysMenuMapper sysMenuMapper;

    @Resource
    SysDepartmentMapper sysDepartmentMapper;

    @Resource
    SysRoleMapper sysRoleMapper;

    @Resource
    SysJobMapper sysJobMapper;

    @Resource
    SysMenuService sysMenuService;

    @Resource
    SysDictMapper sysDictMapper;

    @Resource
    SysUserBranchDepMapper sysUserBranchDepMapper;

    @Resource
    SysLoginLogService sysLoginLogService;

    @Resource
    XxgkProperties xxgkProperties;

    public JsonResult userPage(Page<SysUser> page, String departmentId, String jobId, Short status, String username) {

        // 获取字典信息
        String value = sysDictMapper.selectById(XxgkConstant.SHOW_OPERATOR_DICT_ID).getValue();

        SysUserQo sysUserQo = SysUserQo.builder()
                .departmentId(departmentId)
                .jobId(jobId)
                .status(status)
                .username(username)
                .isShowOperator(Boolean.parseBoolean(value))
                .build();

        IPage<SysUserDepJobProjectDto> sysUserPage = sysUserMapper.getUserDepJobProjectList(page, sysUserQo);
        return JsonResult.success(sysUserPage);
    }

    public JsonResult user(String id) {
        SysUser sysUser = getById(id);
        SysUserDto sysUserDto = BeanUtil.copyProperties(sysUser, SysUserDto.class);

        // 角色信息
        sysUserDto.setRoleIds(
                sysUserRoleMapper.selectList(
                        Wrappers.<SysUserRole>lambdaQuery()
                                .eq(SysUserRole::getUserId, id)
                ).stream().map(SysUserRole::getRoleId).toList()
        );
        // 分管部门信息
        sysUserDto.setBranchDepartmentIds(
                sysUserBranchDepMapper.selectList(
                        Wrappers.<SysUserBranchDep>lambdaQuery()
                                .eq(SysUserBranchDep::getUserId, id)
                ).stream().map(SysUserBranchDep::getDepartmentId).toList()
        );
        return JsonResult.success(sysUserDto);
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult add(SysUserDto sysUserDto) {
        if (StrUtil.isNotBlank(sysUserDto.getId())) {
            return JsonResult.error("新增接口，不允许传id");
        }
        // 校验信息
        checkInfo(sysUserDto);

        // 执行新增
        executionUserAdd(sysUserDto);
        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult fix(SysUserDto sysUserDto) {
        if (StrUtil.isBlank(sysUserDto.getId())) {
            return JsonResult.error("id是必传的");
        }
        executionUserFix(sysUserDto);
        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult delete(String id) {
        executionUserDel(id);
        return JsonResult.success();
    }

    public JsonResult userInfo() {
        String userId = (String) StpUtil.getLoginId();

        // 用户信息
        SysUserVo sysUserVo = BeanUtil.copyProperties(getById(userId), SysUserVo.class);

        // 设置部门信息
        sysUserVo.setSysDepartment(sysDepartmentMapper.getDepartmentByUserId(userId));

        // 设置角色信息
        sysUserVo.setSysRoles(sysRoleMapper.getRoleByUserId(userId));

        // 设置职务信息
        sysUserVo.setSysJob(sysJobMapper.getJobByUserId(userId));

        // 设置项目信息
        sysUserVo.setProject(sysDictMapper.getDictByUserId(userId));

        // 设置分管部门
        sysUserVo.setSysUserBranchDep(sysUserBranchDepMapper.getUserBranchDep(userId));

        // 设置权限信息
        sysUserVo.setSysPermissionIds(
                Optional.ofNullable(sysMenuMapper.getPermissionByUserId(userId))
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(ObjUtil::isNotEmpty)
                        .filter(SysMenu::getShowLink)
                        .map(SysMenu::getAuths)
                        .toList()
        );

        return JsonResult.success(sysUserVo);
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult userEnable(Short enable, String userId) {
        update(Wrappers.<SysUser>lambdaUpdate().set(SysUser::getStatus, enable).eq(SysUser::getId, userId));
        return JsonResult.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public JsonResult jobMove(JobMoveDto jobMoveDto) {

        sysUserMapper.update(
                Wrappers.<SysUser>lambdaUpdate()
                        .set(SysUser::getDepartmentId, jobMoveDto.getDepartmentId())
                        .set(SysUser::getJobId, jobMoveDto.getJobId())
                        .eq(SysUser::getId, jobMoveDto.getUserId())
        );

        return JsonResult.success();
    }


    public JsonResult userMenu() {

        String userId = (String) StpUtil.getLoginId();

        List<SysMenuVo> sysMenuVos = sysMenuService.buildMenuTree1(Optional.ofNullable(sysMenuMapper.getMenuByUserId(userId))
                .orElse(Lists.newArrayList())
                .stream()
                .filter(ObjUtil::isNotEmpty)
                .map(SysMenu::toSysMenuVo)
                .toList());
        return JsonResult.success(sysMenuVos);
    }

    public JsonResult password(PasswordDto passwordDto) {

        // 验证两次是否一致
        if (!StrUtil.equals(passwordDto.getNewPassword(), passwordDto.getConfirmPassword())) {
            return JsonResult.error("两次密码不一致");
        }

        // 验证旧密码是否正确
        if (!StrUtil.equals(getById(passwordDto.getUserId()).getPassword(), DigestUtil.md5Hex(passwordDto.getPassword()))) {
            return JsonResult.error("旧密码不正确");
        }

        sysUserMapper.update(
                Wrappers.<SysUser>lambdaUpdate()
                        .set(SysUser::getPassword, DigestUtil.md5Hex(passwordDto.getConfirmPassword()))
                        .eq(SysUser::getId, passwordDto.getUserId())
        );

        return JsonResult.success();
    }


    //===============================================private=================================================


    private void checkInfo(SysUserDto sysUserDto) throws BusinessException {
        // 校验身份证号
        if (StrUtil.isNotBlank(sysUserDto.getIdNumber()) && !IdcardUtil.isValidCard18(sysUserDto.getIdNumber())) {
            throw new BusinessException("身份证号不合法");
        }
        // 查询用户是否已经存在
        if (StrUtil.isNotBlank(sysUserDto.getUsername())) {
            SysUser one = getOne(
                    Wrappers.<SysUser>lambdaQuery()
                            .eq(SysUser::getUsername, sysUserDto.getUsername())
            );
            if (ObjUtil.isNotNull(one)) {
                throw new BusinessException("该用户名已存在");
            }
        }
        // 查询身份证号是否已经存在
        if (StrUtil.isNotBlank(sysUserDto.getIdNumber())) {
            SysUser one = getOne(
                    Wrappers.<SysUser>lambdaQuery()
                            .eq(SysUser::getIdNumber, sysUserDto.getIdNumber())
            );
            if (ObjUtil.isNotNull(one)) {
                throw new BusinessException("该身份证号已存在");
            }
        }
    }

    private void executionUserAdd(SysUserDto sysUserDto) {

        // 用户基本信息
        sysUserDto.setId(SnowflakeUtil.snowflakeId());
        sysUserDto.setPassword(DigestUtil.md5Hex(sysUserDto.getPassword()));
        save(BeanUtil.copyProperties(sysUserDto, SysUser.class));

        // 角色信息
        sysUserRoleMapper.insert(
                sysUserDto.getRoleIds()
                        .stream()
                        .map(item -> SysUserRole.builder().userId(sysUserDto.getId()).roleId(item).build())
                        .toList()
        );

        // 分管部门
        sysUserBranchDepMapper.insert(
                sysUserDto.getBranchDepartmentIds()
                        .stream()
                        .map(item -> SysUserBranchDep.builder().userId(sysUserDto.getId()).departmentId(item).build())
                        .toList()
        );

    }

    private void executionUserFix(SysUserDto sysUserDto) {

        // 修改用户信息
        updateById(BeanUtil.copyProperties(sysUserDto, SysUser.class));

        // 角色信息 删除
        sysUserRoleMapper.delete(
                Wrappers.<SysUserRole>lambdaQuery()
                        .eq(SysUserRole::getUserId, sysUserDto.getId())
        );
        // 角色信息 替换
        sysUserRoleMapper.insert(
                sysUserDto.getRoleIds()
                        .stream()
                        .map(item -> SysUserRole.builder().userId(sysUserDto.getId()).roleId(item).build())
                        .toList()
        );

        // 分管部门 删除
        sysUserBranchDepMapper.delete(
                Wrappers.<SysUserBranchDep>lambdaQuery()
                        .eq(SysUserBranchDep::getUserId, sysUserDto.getId())
        );

        // 分管部门 替换
        sysUserBranchDepMapper.insert(
                sysUserDto.getBranchDepartmentIds()
                        .stream()
                        .map(item -> SysUserBranchDep.builder().userId(sysUserDto.getId()).departmentId(item).build())
                        .toList()
        );
    }

    /**
     * <h2>删除用户</h2>
     *
     * @param userId:
     * @return boolean
     * <AUTHOR>
     * @date 2024/8/21 16:14
     */
    private void executionUserDel(String userId) {

        // 用户基本信息
        removeById(userId);

        // 角色信息
        sysUserRoleMapper.delete(
                Wrappers.<SysUserRole>lambdaQuery()
                        .eq(SysUserRole::getUserId, userId)
        );

        // 分管部门 删除
        sysUserBranchDepMapper.delete(
                Wrappers.<SysUserBranchDep>lambdaQuery()
                        .eq(SysUserBranchDep::getUserId, userId)
        );

    }

    public JsonResult updateRateBatch(List<String> idss, Boolean isRate) {
        return sysUserMapper.updateRateBatch(idss, isRate) > 0 ? JsonResult.success() : JsonResult.error();
    }

    public IPage<SysUserDepJobProjectDto> queryUserDetailsPage(Page<Object> page, String realName) {
        SysUserQo qo = new SysUserQo();
        qo.setRealName(realName);
        return sysUserMapper.getUserDepJobProjectList(page, qo);
    }


    public JsonResult loginTime() {
        SysLoginLog sysLoginLog = sysLoginLogService.getOne(
                Wrappers.<SysLoginLog>lambdaQuery()
                        .eq(SysLoginLog::getUserId, (String) StpUtil.getLoginId())
                        .orderByDesc(SysLoginLog::getCreateTime)
                        .last("limit 1")

        );
        return JsonResult.success(sysLoginLog);
    }

    public JsonResult restPassword(String userId) {
        update(
                Wrappers.<SysUser>lambdaUpdate()
                        .eq(SysUser::getId, userId)
                        .set(SysUser::getPassword, DigestUtil.md5Hex(xxgkProperties.getDefaultPassword()))
        );
        return JsonResult.success();
    }

    public JsonResult userProjectClassify() {
        String userId = (String) StpUtil.getLoginId();

        List<SysDict> sysDicts = sysDictMapper.getProjectClassifyByUserId(userId);
        return JsonResult.success(sysDicts);
    }


}
