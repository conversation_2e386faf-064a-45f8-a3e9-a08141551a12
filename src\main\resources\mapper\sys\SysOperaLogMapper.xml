<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bjcj.xxgk.mapper.sys.SysOperaLogMapper">
  <resultMap id="BaseResultMap" type="com.bjcj.xxgk.model.pojo.sys.SysOperaLog">
    <!--@Table public.sys_opera_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="req_type" jdbcType="VARCHAR" property="reqType" />
    <result column="req_param" jdbcType="VARCHAR" property="reqParam" />
    <result column="res_param" jdbcType="VARCHAR" property="resParam" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, "module", "type", "desc", "method", req_type, req_param, res_param, user_id, 
    user_name, ip, create_time
  </sql>
</mapper>