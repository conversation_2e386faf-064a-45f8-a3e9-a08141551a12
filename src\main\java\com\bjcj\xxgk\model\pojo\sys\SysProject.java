package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bjcj.xxgk.common.enums.ProjectStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/6 8:39 周五
 */
@Schema(description = "系统项目")
@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_project")
public class SysProject extends BaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    /**
     * 项目类型id
     */
    @TableField(value = "classify")
    @Schema(description = "项目类型id")
    private String classify;

    /**
     * 项目名称
     */
    @TableField(value = "meeting_theme")
    @Schema(description = "会议主题")
    private String meetingTheme;

    /**
     * 项目名称
     */
    @TableField(value = "name")
    @Schema(description = "项目名称")
    private String name;

    /**
     * 评审时间
     */
    @TableField(value = "assessment_time")
    @Schema(description = "评审时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime assessmentTime;

    /**
     * 承办人
     */
    @TableField(value = "leader")
    @Schema(description = "承办人")
    private String leader;

    /**
     * 会议地点
     */
    @TableField(value = "meeting_place")
    @Schema(description = "会议地点")
    private String meetingPlace;

    /**
     * 编写单位
     */
    @TableField(value = "write_workplace")
    @Schema(description = "编写单位")
    private String writeWorkplace;

    /**
     * 评审单位
     */
    @TableField(value = "assessment_workplace")
    @Schema(description = "评审单位")
    private String assessmentWorkplace;

    /**
     * 项目承担单位
     */
    @TableField(value = "undertake_workplace")
    @Schema(description = "项目承担单位")
    private String undertakeWorkplace;

    /**
     * 状态 0入库数据 1成立 2退回
     */
    @TableField(value = "\"status\"")
    @Schema(description = "状态 0入库数据 1成立 2退回")
    private ProjectStatusEnum status;

    /**
     * 文件序列号
     */
    @TableField(value = "serial")
    @Schema(description = "文件序列号")
    private Integer serial = 1;

    /**
     * 抽取设置的JSON字符串
     */
    @TableField(value = "config")
    @Schema(description = "抽取设置的JSON字符串")
    private String config;

    /**
     * 回避专家的信息JSON字符串
     */
    @TableField(value = "avoid")
    @Schema(description = "回避专家的信息JSON字符串")
    private String avoid;

    /**
     * 回避专家的信息JSON字符串
     */
    @TableField(value = "phone")
    @Schema(description = "联系方式")
    private String phone;
}