package com.bjcj.xxgk.model.pojo.sys;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/20 15:08 周二
 */
@Schema(description = "部门表")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_department")
public class SysDepartment {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "")
    private String id;

    @TableField(value = "\"name\"")
    @Schema(description = "名称")
    private String name;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    @TableField(value = "\"status\"")
    @Schema(description = "状态 0未启用 1正常")
    private Short status;

    @TableField(value = "parent_id")
    @Schema(description = "父id")
    private String parentId;

    @TableField(value = "code")
    @Schema(description = "部门代码")
    private String code;

    @TableField(value = "sort")
    @Schema(description = "排序")
    private Short sort;

    @TableField(value = "\"operator\"")
    @Schema(description = "操作人")
    private String operator;
}