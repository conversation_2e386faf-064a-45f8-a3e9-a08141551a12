package com.bjcj.xxgk.serviceImpl.sys;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bjcj.xxgk.common.domain.JsonResult;
import com.bjcj.xxgk.common.utils.LocalDateTimeUtil;
import com.bjcj.xxgk.mapper.sys.SysDictTypeMapper;
import com.bjcj.xxgk.model.pojo.sys.SysDictType;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/8/29 16:15 周四
 */
@Service
public class SysDictTypeService extends ServiceImpl<SysDictTypeMapper, SysDictType> {

    public JsonResult pageList(Page<SysDictType> page, String dictName, String dictCode, Short status,
                               String dictType, String startTime, String endTime) {

        Page<SysDictType> result = page(
                page,
                Wrappers.<SysDictType>lambdaQuery()
                        .eq(StrUtil.isNotBlank(dictName), SysDictType::getName, dictName)
                        .eq(StrUtil.isNotBlank(dictCode), SysDictType::getCode, dictCode)
                        .eq(ObjUtil.isNotEmpty(status), SysDictType::getStatus, status)
                        .eq(StrUtil.isNotBlank(dictType), SysDictType::getType, dictType)
                        .between(
                                StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime),
                                SysDictType::getCreateTime,
                                LocalDateTimeUtil.zeroFill(startTime),
                                LocalDateTimeUtil.zeroFill(endTime)
                        )
                        .orderByDesc(SysDictType::getCreateTime)
        );
        return JsonResult.success(result);
    }
}
