package com.bjcj.xxgk.mapper.sys;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bjcj.xxgk.model.dto.sys.SysUserDepJobProjectDto;
import com.bjcj.xxgk.model.pojo.sys.SysUser;
import com.bjcj.xxgk.model.qo.BkhUserQo;
import com.bjcj.xxgk.model.qo.SysUserDeptJobQo;
import com.bjcj.xxgk.model.qo.SysUserQo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-09-20 10:46 周五
 */
public interface SysUserMapper extends BaseMapper<SysUser> {
    /**
     * <h2>获取用户列表 包含部门职务角色</h2>
     *
     * @param sysUserQo:
     * @return java.util.List<com.bjcj.kpi.model.dto.sys.SysUserDepJobDto>
     * <AUTHOR>
     * @date 2024/9/2 14:03
     */
    IPage<SysUserDepJobProjectDto> getUserDepJobProjectList(IPage<?> page, @Param("sysUserQo") SysUserQo sysUserQo);

    List<SysUserDepJobProjectDto> getUserDepJobProjectListNotPage(@Param("sysUserQo") SysUserQo sysUserQo);

    IPage<SysUserDepJobProjectDto> getUserByRoleId(IPage<?> page, @Param("roleId") String roleId,
                                                   @Param("isShowOperator") Boolean isShowOperator);

    int updateRateBatch(@Param("idss") List<String> idss, @Param("isRate") Boolean isRate);

    int selectRateCount();

    Page<SysUser> reportOfBkhUser(
            IPage<?> page,
            @Param("cycleId") String cycleId,
            @Param("realName") String realName,
            @Param("reportSubmitStatus") Boolean reportSubmitStatus,
            @Param("userIds") List<String> userIds
    );

    SysUserDepJobProjectDto getUserDepJobProjectById(String userId);

    Page<SysUser> allReportOfUser(
            IPage<?> page,
            @Param("cycleId") String cycleId,
            @Param("realName") String realName,
            @Param("reportSubmitStatus") Boolean reportSubmitStatus
    );

    Integer selectDeptRateUserCount(String id);

    List<SysUserDeptJobQo> selectUserDetailsInfoByJobId(String jobId);

    List<SysUserDeptJobQo> selectUserDetailsInfoByDeptIdAndJobId(
            @Param("deptId") String bkhUserDeptId,
            @Param("jobId") String bkhUserJobId
    );

    List<BkhUserQo> selectAllYearBkhUserInfo(
            @Param("userId") String userId
    );

    List<SysUserDepJobProjectDto> selectAllBkhUserInfo(String userId);
}