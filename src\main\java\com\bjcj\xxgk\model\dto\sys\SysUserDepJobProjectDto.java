package com.bjcj.xxgk.model.dto.sys;

import com.bjcj.xxgk.model.pojo.sys.SysDepartment;
import com.bjcj.xxgk.model.pojo.sys.SysDict;
import com.bjcj.xxgk.model.pojo.sys.SysJob;
import com.bjcj.xxgk.model.pojo.sys.SysUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2024/8/21 10:14 周三
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SysUserDepJobProjectDto extends SysUser {
    
    private SysDepartment sysDepartment;

    private SysJob sysJob;

    private SysDict project;

}
